using shared.Components.MultiStepProcess.Enums;
using shared.Models.Interfaces;

namespace shared.Components.MultiStepProcess.Interfaces
{
    /// <summary>
    /// Non-generic base interface for step definitions to allow storage in collections.
    /// </summary>
    /// <typeparam name="TState">The type of state used by the IStateful object</typeparam>
    /// <typeparam name="TObject">The type of the payload object that implements IStateful</typeparam>
    public interface IMultiStepProcessStepDefinition<TState, TObject>
        where TObject : IStateful<TState>
        where TState : struct
    {
        /// <summary>
        /// The state this step definition applies to.
        /// </summary>
        TState State { get; set; }

        /// <summary>
        /// Optional timeout for the StateTask. If null, uses process default or no timeout.
        /// </summary>
        TimeSpan? StateTaskTimeout { get; set; }

        /// <summary>
        /// Optional timeout for the RollbackTask. If null, uses process default or no timeout.
        /// </summary>
        TimeSpan? RollbackTaskTimeout { get; set; }

        /// <summary>
        /// Optional description of what this step does.
        /// </summary>
        string? Description { get; set; }

        /// <summary>
        /// Maximum number of retries allowed for the StateTask (forward operation).
        /// If null, uses the process-level MaxRetries configuration.
        /// </summary>
        int? StateTaskMaxRetries { get; set; }

        /// <summary>
        /// Maximum number of retries allowed for the RollbackTask (backward operation).
        /// If null, uses the process-level MaxRetries configuration.
        /// </summary>
        int? RollbackTaskMaxRetries { get; set; }

        /// <summary>
        /// Maximum number of retries allowed for this step (applies to both StateTask and RollbackTask).
        /// If null, uses the process-level MaxRetries configuration.
        /// Takes precedence over StateTaskMaxRetries and RollbackTaskMaxRetries if set.
        /// </summary>
        int? MaxRetries { get; set; }

        /// <summary>
        /// The next state to transition to when StateTask succeeds.
        /// Null indicates this is the final state (process complete).
        /// </summary>
        TState? NextState { get; set; }

        /// <summary>
        /// The previous state to transition to when RollbackTask succeeds.
        /// Null indicates this is the first state (rollback complete).
        /// </summary>
        TState? PreviousState { get; set; }

        /// <summary>
        /// Whether this is the final step in the process.
        /// </summary>
        bool IsFinalStep { get; }

        /// <summary>
        /// Whether this is the first step in the process.
        /// </summary>
        bool IsFirstStep { get; }

        /// <summary>
        /// Executes the StateTask with the given object.
        /// </summary>
        Task<(MultiStepProcessTaskRunResult, object?)> ExecuteStateTaskAsync(TObject obj);

        /// <summary>
        /// Executes the RollbackTask with the given object.
        /// </summary>
        Task<(MultiStepProcessTaskRunResult, object?)> ExecuteRollbackTaskAsync(TObject obj);

        /// <summary>
        /// Gets the effective maximum retries for this step.
        /// Returns step-level MaxRetries if set, otherwise falls back to process-level maxRetries.
        /// </summary>
        /// <param name="processMaxRetries">Process-level maximum retries to use as fallback</param>
        /// <returns>The effective maximum retries for this step</returns>
        int GetEffectiveMaxRetries(int processMaxRetries);

        /// <summary>
        /// Gets the effective maximum retries for the specified task type in this step.
        /// Returns task-specific MaxRetries if set, otherwise falls back to process-level maxRetries.
        /// </summary>
        /// <param name="processState">The current process state (Running or RollingBack)</param>
        /// <param name="processMaxRetries">Process-level maximum retries to use as fallback</param>
        /// <returns>The effective maximum retries for the specified task type</returns>
        int GetEffectiveMaxRetries(MultiStepProcessState processState, int processMaxRetries);

        /// <summary>
        /// Gets the appropriate task based on the process state.
        /// </summary>
        /// <param name="processState">Current process state</param>
        /// <returns>The task to execute</returns>
        Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> GetTaskForProcessState(MultiStepProcessState processState);

        /// <summary>
        /// Gets the appropriate timeout based on the process state.
        /// </summary>
        /// <param name="processState">Current process state</param>
        /// <returns>The timeout for the task, or null if no timeout</returns>
        TimeSpan? GetTimeoutForProcessState(MultiStepProcessState processState);

        /// <summary>
        /// Gets the next state to transition to based on the process state.
        /// </summary>
        /// <param name="processState">Current process state</param>
        /// <returns>The next state to transition to, or null if this is a terminal state</returns>
        TState? GetNextStateForProcessState(MultiStepProcessState processState);
    }

    /// <summary>
    /// Generic interface for step definitions with strongly-typed results.
    /// </summary>
    /// <typeparam name="TState">The type of state used by the IStateful object</typeparam>
    /// <typeparam name="TObject">The type of the payload object that implements IStateful</typeparam>
    /// <typeparam name="TResult">The type of result returned by the tasks</typeparam>
    public interface IMultiStepProcessStepDefinition<TState, TObject, TResult> : IMultiStepProcessStepDefinition<TState, TObject>
        where TObject : IStateful<TState>
        where TState : struct
    {
        /// <summary>
        /// The task to execute when the process is in Running state.
        /// This task moves the process forward.
        /// </summary>
        Func<TObject, Task<(MultiStepProcessTaskRunResult, TResult?)>> StateTask { get; set; }

        /// <summary>
        /// The task to execute when the process is in RollingBack state.
        /// This task undoes the work done by the StateTask.
        /// </summary>
        Func<TObject, Task<(MultiStepProcessTaskRunResult, TResult?)>> RollbackTask { get; set; }
    }
}
