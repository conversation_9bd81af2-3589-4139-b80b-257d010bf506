using Amazon.SQS;
using Amazon.SQS.Model;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using shared.Components.MessageBus.Configuration;
using shared.Components.MessageBus.Interfaces;
using shared.Components.MessageBus.Models;
using shared.Extensions;
using shared.Helpers;
using shared.Models.Configuration;
using shared.Models.Enums;
using shared.Services;
using System.Text.Json;

namespace shared.Components.MessageBus.Implementation
{
    /// <summary>
    /// AWS SQS implementation of the message bus
    /// </summary>
    public class SQSMessageBus : MessageBusBase
    {
        private readonly IAmazonSQS _amazonSQS;
        private readonly IHostApplicationLifetime _lifetime;
        private Task? _messageProcessingTask;

        public SQSMessageBus(
            ILogger<SQSMessageBus> logger,
            IAmazonSQS amazonSQS,
            ISecretsService secretsService,
            IOptions<MicroserviceConfiguration> microserviceConfiguration,
            IOptions<MessageBusConfiguration> messageBusConfiguration,
            IServiceProvider serviceProvider,
            IMessageBusQueueManager queueManager,
            IHostApplicationLifetime lifetime,
            IMessageDeduplicationService? deduplicationService = null)
            : base(logger, secretsService, microserviceConfiguration, messageBusConfiguration, serviceProvider, queueManager, deduplicationService)
        {
            _amazonSQS = amazonSQS;
            _lifetime = lifetime;
        }

        /// <summary>
        /// Send message to external SQS queue
        /// </summary>
        protected override async Task<string?> SendToExternalQueue(MessageBusMessage message)
        {
            try
            {
                var queueUrl = await _queueManager.GetQueueUrlAsync(message.TargetMicroservice);
                if (string.IsNullOrEmpty(queueUrl))
                {
                    _logger.LogError("Could not get queue URL for microservice {MicroserviceType}", message.TargetMicroservice);
                    return null;
                }

                var sendMessageRequest = new SendMessageRequest
                {
                    QueueUrl = queueUrl,
                    MessageBody = JsonSerializer.Serialize(message),
                    DelaySeconds = message.DelayInSeconds
                };

                // Add message attributes for better filtering/routing
                sendMessageRequest.MessageAttributes = new Dictionary<string, MessageAttributeValue>();
                sendMessageRequest.MessageAttributes.Add("MessageId", new MessageAttributeValue
                {
                    StringValue = message.Id,
                    DataType = "String"
                });

                sendMessageRequest.MessageAttributes.Add("Source", new MessageAttributeValue
                {
                    StringValue = message.Source,
                    DataType = "String"
                });

                sendMessageRequest.MessageAttributes.Add("Controller", new MessageAttributeValue
                {
                    StringValue = message.Controller,
                    DataType = "String"
                });

                sendMessageRequest.MessageAttributes.Add("Route", new MessageAttributeValue
                {
                    StringValue = message.Route,
                    DataType = "String"
                });

                var response = await _amazonSQS.SendMessageAsync(sendMessageRequest);

                if (!string.IsNullOrEmpty(response.MessageId))
                {
                    _logger.LogInformation("Successfully sent message {MessageId} to queue {QueueUrl}",
                        message.Id, queueUrl);
                    return message.Id;
                }
                else
                {
                    _logger.LogError("Failed to send message {MessageId} to queue {QueueUrl} - no message ID returned",
                        message.Id, queueUrl);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send message {MessageId} to external queue", message.Id);
                return null;
            }
        }

        /// <summary>
        /// Start the hosted service and begin processing messages
        /// </summary>
        public override async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting SQS Message Bus for microservice {MicroserviceType}",
                _microserviceConfiguration.Type);

            // Ensure our own queue exists
            if (!await _queueManager.EnsureQueueExistsAsync(_microserviceConfiguration.Type))
            {
                _logger.LogError("Failed to ensure queue exists for microservice {MicroserviceType}",
                    _microserviceConfiguration.Type);
                throw new InvalidOperationException($"Could not create or access queue for microservice {_microserviceConfiguration.Type}");
            }

            /*if (!await StartupHelper.WaitForAppStartup(_lifetime, cancellationToken))
            {
                throw new InvalidOperationException($"Could not wait for HTTP listener to start");
            }*/

            _messageProcessingTask = Task.Run(async () => await ProcessMessagesAsync(_cancellationTokenSource.Token),
                cancellationToken);
        }

        /// <summary>
        /// Stop the hosted service
        /// </summary>
        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Stopping SQS Message Bus");
            
            _cancellationTokenSource.Cancel();
            
            if (_messageProcessingTask != null)
            {
                try
                {
                    await _messageProcessingTask;
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                }
            }

            await base.StopAsync(cancellationToken);
        }

        /// <summary>
        /// Main message processing loop
        /// </summary>
        private async Task ProcessMessagesAsync(CancellationToken cancellationToken)
        {
            var selfQueueUrl = await _queueManager.GetQueueUrlAsync(_microserviceConfiguration.Type);

            _logger.LogInformation("Starting message processing loop for queue {QueueUrl}", selfQueueUrl);

            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    var messages = await GetMessagesAsync(selfQueueUrl, cancellationToken);

                    if (messages?.Messages != null && messages.Messages.Count > 0)
                    {
                        var processingTasks = messages.Messages.Select(sqsMessage =>
                            ProcessSingleMessageAsync(sqsMessage, selfQueueUrl, cancellationToken)
                        ).ToArray();

                        await Task.WhenAll(processingTasks);
                    }
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in message processing loop");

                    // Wait a bit before retrying to avoid tight error loops
                    try
                    {
                        await Task.Delay(TimeSpan.FromSeconds(5), cancellationToken);
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                }
            }

            _logger.LogInformation("Message processing loop stopped");
        }

        /// <summary>
        /// Get messages from SQS queue
        /// </summary>
        private async Task<ReceiveMessageResponse> GetMessagesAsync(string queueUrl, CancellationToken cancellationToken)
        {
            var request = new ReceiveMessageRequest
            {
                QueueUrl = queueUrl,
                MaxNumberOfMessages = _messageBusConfiguration.MaxConcurrentMessages,
                WaitTimeSeconds = _messageBusConfiguration.LongPollingWaitTimeSeconds,
                VisibilityTimeout = _messageBusConfiguration.VisibilityTimeoutSeconds,
                MessageAttributeNames = new List<string> { "All" }
            };

            return await _amazonSQS.ReceiveMessageAsync(request, cancellationToken);
        }

        /// <summary>
        /// Process a single SQS message
        /// </summary>
        private async Task ProcessSingleMessageAsync(Message sqsMessage, string queueUrl, CancellationToken cancellationToken)
        {
            MessageBusMessage? message = null;

            try
            {
                // Deserialize the message
                message = JsonSerializer.Deserialize<MessageBusMessage>(sqsMessage.Body);
                if (message == null)
                {
                    _logger.LogWarning("Received null message from SQS, deleting message");
                    await DeleteMessageAsync(sqsMessage, queueUrl);
                    return;
                }

                // Store SQS-specific metadata for message confirmation
                message.Metadata["SQS_ReceiptHandle"] = sqsMessage.ReceiptHandle;
                message.Metadata["SQS_QueueUrl"] = queueUrl;

                // Check deduplication if enabled
                if (_deduplicationService != null)
                {
                    if (!_deduplicationService.ShouldProcessMessage(message.Id))
                    {
                        _logger.LogInformation("Skipping duplicate message {MessageId}", message.Id);
                        await DeleteMessageAsync(sqsMessage, queueUrl);
                        return;
                    }
                }

                _logger.LogInformation("Processing message {MessageId} for endpoint {Endpoint}",
                    message.Id, message.GetEndpoint());



                // Process the message asynchronously without blocking the consumer
                _ = Task.Run(async () => await ProcessMessageAsync(message, sqsMessage, queueUrl))
                    .ContinueWith(task =>
                    {
                        if (task.IsFaulted)
                        {
                            _logger.LogError(task.Exception, "Unhandled error in message processing for {MessageId}", message.Id);
                        }
                    }, TaskScheduler.Default);

                _logger.LogDebug("Message {MessageId} dispatched for async processing", message.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error dispatching SQS message {ReceiptHandle}", sqsMessage.ReceiptHandle);

                if (message != null)
                {
                    // Handle failed dispatch
                    _ = Task.Run(async () => await HandleFailedMessage(message, sqsMessage, queueUrl, ex.Message));
                }
                else
                {
                    // If we can't even deserialize the message, delete it
                    await DeleteMessageAsync(sqsMessage, queueUrl);
                }
            }
        }

        /// <summary>
        /// Process message asynchronously in background
        /// </summary>
        private async Task ProcessMessageAsync(MessageBusMessage message, Message sqsMessage, string queueUrl)
        {
            try
            {
                // Process the message locally
                var success = await TryInvokeLocalMethod(message);

                if (!success)
                {
                    // Fallback to HTTP call
                    var result = await FallbackToHttpCall(message);
                    success = !string.IsNullOrEmpty(result);
                }

                if (success)
                {
                    // Message processed successfully, confirm and delete from queue
                    var confirmResult = await ConfirmMessage(message);

                    if (confirmResult == MessageBusResult.Success)
                    {
                        _logger.LogInformation("Successfully processed and confirmed message {MessageId}", message.Id);
                    }
                    else
                    {
                        _logger.LogError("Failed to confirm message {MessageId} after successful processing", message.Id);
                    }
                }
                else
                {
                    // Message failed, handle retry logic
                    await HandleFailedMessage(message, sqsMessage, queueUrl);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in async message processing for {MessageId}", message.Id);
                await HandleFailedMessage(message, sqsMessage, queueUrl, ex.Message);
            }
        }

        /// <summary>
        /// Handle a failed message with retry logic
        /// </summary>
        private async Task HandleFailedMessage(MessageBusMessage message, Message sqsMessage, string queueEndpoint, string? errorMessage = null)
        {
            message.IncrementRetry(errorMessage);
            
            if (message.CanRetry())
            {
                _logger.LogWarning("Message {MessageId} failed, retry {RetryCount}/{MaxRetries}",
                    message.Id, message.RetryCount, message.MaxRetries);

                // Remove from deduplication cache to allow reprocessing
                if (_deduplicationService != null)
                {
                    _deduplicationService.MarkMessageRequeued(message.Id);
                }

                // Calculate exponential backoff delay
                var delaySeconds = Math.Min(300, (int)Math.Pow(2, message.RetryCount)); // Max 5 minutes

                // Re-send message with delay
                var retryMessage = JsonSerializer.Serialize(message);
                var sendRequest = new SendMessageRequest
                {
                    QueueUrl = queueEndpoint,
                    MessageBody = retryMessage,
                    DelaySeconds = delaySeconds
                };

                await _amazonSQS.SendMessageAsync(sendRequest);

                // Delete original message
                await DeleteMessageAsync(sqsMessage, queueEndpoint);

                _logger.LogInformation("Re-queued message {MessageId} with {DelaySeconds}s delay",
                    message.Id, delaySeconds);
            }
            else
            {
                _logger.LogError("Message {MessageId} failed permanently after {RetryCount} retries",
                    message.Id, message.RetryCount);

                message.MarkFailed(errorMessage);
                
                // Delete the message to prevent infinite processing
                await DeleteMessageAsync(sqsMessage, queueEndpoint);
            }
        }

        /// <summary>
        /// Delete a message from the SQS queue
        /// </summary>
        private async Task DeleteMessageAsync(Message message, string queueEndpoint)
        {
            try
            {
                await _amazonSQS.DeleteMessageAsync(new DeleteMessageRequest
                {
                    QueueUrl = queueEndpoint,
                    ReceiptHandle = message.ReceiptHandle
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete message {ReceiptHandle} from queue", message.ReceiptHandle);
            }
        }

        /// <summary>
        /// Confirm successful processing of a message and delete it from the queue
        /// </summary>
        public override async Task<MessageBusResult> ConfirmMessage(MessageBusMessage message)
        {
            try
            {
                // Extract SQS-specific metadata
                if (!message.Metadata.TryGetValue("SQS_ReceiptHandle", out var receiptHandleObj) ||
                    !message.Metadata.TryGetValue("SQS_QueueUrl", out var queueUrlObj))
                {
                    _logger.LogError("Message {MessageId} missing SQS metadata for confirmation", message.Id);
                    return MessageBusResult.Error;
                }

                var receiptHandle = receiptHandleObj.ToString();
                var queueUrl = queueUrlObj.ToString();

                if (string.IsNullOrEmpty(receiptHandle) || string.IsNullOrEmpty(queueUrl))
                {
                    _logger.LogError("Message {MessageId} has invalid SQS metadata for confirmation", message.Id);
                    return MessageBusResult.Error;
                }

                // Delete the message from SQS
                await _amazonSQS.DeleteMessageAsync(new DeleteMessageRequest
                {
                    QueueUrl = queueUrl,
                    ReceiptHandle = receiptHandle
                });

                // Mark message as completed
                message.MarkCompleted();

                _logger.LogInformation("Successfully confirmed and deleted message {MessageId}", message.Id);
                return MessageBusResult.Success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to confirm message {MessageId}", message.Id);
                return MessageBusResult.Error;
            }
        }

    }
}
