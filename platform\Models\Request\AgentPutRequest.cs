using System.ComponentModel.DataAnnotations;

namespace platform.Models.Request
{
    public class AgentPutRequest
    {
        [Required(ErrorMessage = "Name is required")]
        [MinLength(5, ErrorMessage = "Name must be at least 3 characters loing")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "Description is required")]
        [MinLength(5, ErrorMessage = "Description must be at least 3 characters loing")]
        public string Description { get; set; } = string.Empty;

        [Required(ErrorMessage = "Default Prompt is required")]
        [MinLength(20, ErrorMessage = "Default Prompt must be at least 20 characters loing")]
        public string DefaultPrompt { get; set; } = string.Empty;
    }
}
