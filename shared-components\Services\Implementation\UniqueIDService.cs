using Microsoft.Extensions.Options;
using shared.Extensions;
using shared.Models.Configuration;
using shared.Models.Enums;
using System.Collections.Concurrent;

namespace shared.Services.Implementation
{
    /// <summary>
    /// Service for generating unique IDs for models across microservices.
    /// Generates IDs by concatenating microservice type, instance ID, model type prefix, and a base64-encoded UUID.
    /// </summary>
    public class UniqueIDService : IUniqueIDService
    {
        private readonly MicroserviceConfiguration _microserviceConfiguration;
        
        // Cache for model type prefixes to avoid repeated enum description lookups
        private static readonly ConcurrentDictionary<ModelType, string> _modelTypePrefixCache = 
            new ConcurrentDictionary<ModelType, string>();

        public UniqueIDService(IOptions<MicroserviceConfiguration> microserviceConfiguration)
        {
            _microserviceConfiguration = microserviceConfiguration.Value;
        }

        /// <summary>
        /// Generates a unique ID by concatenating microservice type, instance ID, model type prefix, and a base64-encoded UUID.
        /// Format: {MicroserviceType}:{InstanceId}:{ModelTypePrefix}:{Base64UUID}
        /// </summary>
        /// <param name="modelType">The type of model for which to generate the ID</param>
        /// <returns>A unique ID string</returns>
        public async Task<string> GenerateUniqueIdAsync(ModelType modelType)
        {
            // Get microservice type display name (e.g., "Platform", "AgentService")
            var microserviceType = _microserviceConfiguration.Type.DisplayName();
            
            // Get instance ID
            var instanceId = _microserviceConfiguration.InstanceId;
            
            // Get model type prefix from cache or compute it
            var modelTypePrefix = _modelTypePrefixCache.GetOrAdd(modelType, mt => mt.GetDescription());
            
            // Generate UUID and convert to base64
            var uuid = Guid.NewGuid();
            var base64Uuid = ConvertUuidToBase64(uuid);

            // Concatenate all parts
            //var uniqueId = $"{microserviceType}:{instanceId}:{modelTypePrefix}:{base64Uuid}";
            var uniqueId = $"{instanceId}-{modelTypePrefix}-{base64Uuid}";

            // Return as completed task to simulate async operation
            return await Task.FromResult(uniqueId);
        }

        /// <summary>
        /// Converts a UUID from hexadecimal to base64 format for shorter IDs.
        /// </summary>
        /// <param name="uuid">The UUID to convert</param>
        /// <returns>Base64-encoded UUID string</returns>
        private static string ConvertUuidToBase64(Guid uuid)
        {
            // Convert UUID to byte array
            var uuidBytes = uuid.ToByteArray();
            
            // Convert to base64 and remove padding characters for shorter length
            var base64String = Convert.ToBase64String(uuidBytes)
                .TrimEnd('=')  // Remove padding
                .Replace('+', '-')  // Make URL-safe
                .Replace('/', '_'); // Make URL-safe
            
            return base64String;
        }
    }
}
