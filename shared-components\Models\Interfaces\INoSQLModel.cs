﻿using System.Reflection;

namespace shared.Models.Interfaces
{
    public interface INoSQLModel
    {
        /// <summary>
        /// Gets the hash key value from the model instance.
        /// Override in derived classes to provide specific implementation.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key value</returns>
        public string? GetHashKeyValue(string? indexName = null);

        /// <summary>
        /// Gets the range key value from the model instance.
        /// Override in derived classes to provide specific implementation.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key value</returns>
        public string? GetRangeKeyValue(string? indexName = null);

        /// <summary>
        /// Generates search string for the model.
        /// Override in derived classes to provide specific search logic.
        /// </summary>
        /// <returns>Search string</returns>
        public string GetSearchString();

        /// <summary>
        /// Gets property value by name using reflection.
        /// </summary>
        /// <param name="propertyName">Property name</param>
        /// <returns>Property value</returns>
        protected object? GetPropertyValue(string propertyName)
        {
            PropertyInfo? property = GetType().GetProperty(propertyName);
            return property?.GetValue(this);
        }

        /// <summary>
        /// Sets property value by name using reflection.
        /// </summary>
        /// <param name="propertyName">Property name</param>
        /// <param name="value">Value to set</param>
        protected void SetPropertyValue(string propertyName, object? value)
        {
            PropertyInfo? property = GetType().GetProperty(propertyName);
            property?.SetValue(this, value);
        }

        /// <summary>
        /// Gets property by attribute type using reflection.
        /// </summary>
        /// <param name="type">Type to search</param>
        /// <param name="attributeType">Attribute type to find</param>
        /// <returns>Property info</returns>
        public static PropertyInfo? GetPropertyByAttribute(Type type, Type attributeType)
        {
            return type.GetProperties()
                .Select(pi => new { Property = pi, Attribute = pi.GetCustomAttributes(attributeType, true).FirstOrDefault() })
                .Where(x => x.Attribute != null)
                .FirstOrDefault()?.Property;
        }
    }
}
