﻿using shared.Models.Documents;

namespace shared.Helpers
{
    public static class AWSHelper
    {
        public static string GenerateToken(string hashkey, string rangekey = "")
        {
            if(hashkey.Length == 0)
            {
                throw new ArgumentException("GenerateToken hashkey param needs to be non-empty");
            }
            var concatenatedToken = hashkey + "-" + rangekey;
            while( concatenatedToken.Length < 33)
            {
                concatenatedToken = concatenatedToken + "-" + concatenatedToken;
            }

            return concatenatedToken;
        }

        public static string GenerateToken(NoSQLModel noSQLModel)
        {
            if (noSQLModel == null)
            {
                throw new ArgumentNullException("noSQLModel can't be null.");
            }

            string hashkey = noSQLModel.GetHashKeyValue() ?? "";
            string rangekey = noSQLModel.GetRangeKeyValue() ?? "";

            return GenerateToken(hashkey, rangekey);
        }
    }
}
