using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using Amazon.Runtime.Internal;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using platform.Components.Core;
using platform.Constants;
using platform.Models.Configuration;
using platform.Models.Request;
using platform.Models.Request.Agent;
using platform.Models.Response;
using platform.Services;
using shared.Components.ApiEventBus;
using shared.Components.MessageBus.Interfaces;
using shared.Components.MessageBus.Models;
using shared.Components.MultiStepProcess;
using shared.Components.MultiStepProcess.Enums;
using shared.Components.MultiStepProcess.Interfaces;
using shared.Components.MultiStepProcess.Models;
using shared.Controllers;
using shared.Converters;
using shared.Extensions;
using shared.Helpers;
using shared.Models.Documents.DynamoDB;
using shared.Models.Documents.DynamoDB.ProviderData;
using shared.Models.Enums;
using shared.Models.Interfaces;
using shared.Models.Response;
using shared.Services;
using System.Reflection;

namespace platform.Controllers
{
    [Route(Routes.AgentController.BasePath)]
    [Produces("application/json")]
    public class AgentController : SuperController2
    {
        private readonly ILogger<AgentController> logger;
        private readonly Amazon.BedrockAgent.IAmazonBedrockAgent bedrockAgent;
        private readonly IOptionsMonitor<BedrockConfiguration> bedrockConfiguration;
        private readonly IIAM iAM;

        public AgentController(ILogger<AgentController> logger, IApiEventBus apiEventBus, IDynamoDBContext dynamoDBContext, Amazon.BedrockAgent.IAmazonBedrockAgent bedrockAgent, IIAM iAM, IOptionsMonitor<BedrockConfiguration> bedrockConfiguration, IMessageBus messageBus, IUniqueIDService uniqueIDService) : base(dynamoDBContext, messageBus, uniqueIDService)
        {
            this.logger = logger;
            this.bedrockAgent = bedrockAgent;
            this.bedrockConfiguration = bedrockConfiguration;
            this.iAM = iAM;
        }


        #region CREATE PROCESS
        private IMultiStepProcess<AgentStatus, AwsAgent, SuperControllerMultiStepResult> GetCreationProcess()
        {

            var process = new MultiStepProcess<AgentStatus, AwsAgent, SuperControllerMultiStepResult>()
                .DefineTransition(
                    AgentStatus.QUEUED,
                    AgentStatus.DB_ENTRY_CREATED,
                    new MultiStepProcessTransitionDetails<AwsAgent, SuperControllerMultiStepResult>(CreateProcessCreateDBEntry, maxRetries: 10),
                    new MultiStepProcessTransitionDetails<AwsAgent, SuperControllerMultiStepResult>(CreateProcessRollbackCreateDBEntry, maxRetries: 10),
                    "Create DB Entry"
                )
                .DefineTransition(
                    AgentStatus.DB_ENTRY_CREATED,
                    AgentStatus.CREATING,
                    new MultiStepProcessTransitionDetails<AwsAgent, SuperControllerMultiStepResult>(CreateProcessCreateAgent, maxRetries: 10),
                    new MultiStepProcessTransitionDetails<AwsAgent, SuperControllerMultiStepResult>(DeleteAgentBaseTask, maxRetries: 10),
                    "Create KnowledgeBase"
                )
                .DefineTransition(
                    AgentStatus.CREATING,
                    AgentStatus.READY,
                    new MultiStepProcessTransitionDetails<AwsAgent, SuperControllerMultiStepResult>(CreateProcessCheckAgentStatus, maxRetries: 20),
                    new MultiStepProcessTransitionDetails<AwsAgent, SuperControllerMultiStepResult>(CreateProcessChecKAgentStatusRollback, maxRetries: 20),
                    "Create Datasource and finish"
                );

            return process;
        }


        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost]
        public async Task<IActionResult> CreateAgent([FromBody] AgentCreateRequest request)
        {
            if(!ModelState.IsValid) return BadRequest(ModelState);

            string id = await uniqueIDService.GenerateUniqueIdAsync(ModelType.Agent) ;

            AwsAgent awsAgent = new AwsAgent();
            awsAgent.Name = request.Name;
            awsAgent.Description = request.Description;
            awsAgent.Status = shared.Models.Enums.AgentStatus.QUEUED;
            awsAgent.AgentId = id;
            awsAgent.AccountId = GetAccountId();
            awsAgent.DefaultPrompt = request.Instructions;

            var process = GetCreationProcess();
            process.Initialize(awsAgent);

            MessageBusDispachParams messageBusDispachParams = new MessageBusDispachParams()
            {
                Controller = Routes.AgentController.BasePath,
                Method = HttpMethod.Post,
                DelayInSeconds = 0,
                Route = Constants.Routes.AgentController.Internal.CREATE_PROCESS,
                TargetMicroservice = MicroserviceType.Platform,
                Payload = process.StateData
            };

            await this.DispatchApiEventV2(messageBusDispachParams);

            await PutDBEntry2<AwsAgent>(awsAgent);

            return Ok(awsAgent);
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> CreateProcessCreateDBEntry(AwsAgent agent)
        {
            if (await PutDBEntry2(agent))
            {
                return await Task.FromResult((MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult() { ShouldUpdateDb = false }));
            }
            else
            {
                return await Task.FromResult((MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 10 }));
            }

        }
        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> CreateProcessRollbackCreateDBEntry(AwsAgent knowledgeBase)
        {
            return (await DeleteDBEntry(knowledgeBase)) ? (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult()) : (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { ShouldUpdateDb = false });
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> CreateProcessCreateAgent(AwsAgent agent)
        {

            var createAgentRequest = new Amazon.BedrockAgent.Model.CreateAgentRequest();
            createAgentRequest.AgentName = agent.AgentId;
            createAgentRequest.Description = agent.Description;
            createAgentRequest.AgentResourceRoleArn = $"arn:aws:iam::{await iAM.GetProviderAccountNumber()}:role/service-role/{bedrockConfiguration.CurrentValue.AgentRoleName}";
            createAgentRequest.FoundationModel = agent.LLMModelType.DisplayName();
            createAgentRequest.Instruction = agent.DefaultPrompt;
            createAgentRequest.ClientToken = AWSHelper.GenerateToken(agent.AgentId);

            try
            {
                var resp = await bedrockAgent.CreateAgentAsync(createAgentRequest);
                agent.AwsData.AgentId = resp.Agent.AgentId;
                return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult() { DelayInSeconds = 10 });
            }
            catch (Exception ex)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 10 });
            }
        }
        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> DeleteAgentBaseTask(AwsAgent agent)
        {

            var getAgentRequest = new Amazon.BedrockAgent.Model.GetAgentRequest();
            getAgentRequest.AgentId = agent.AwsData.AgentId;
            var resp = await bedrockAgent.GetAgentAsync(getAgentRequest);
            if (resp.Agent.AgentStatus == Amazon.BedrockAgent.AgentStatus.FAILED)
            {
                return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
            }
            else if (resp.Agent.AgentStatus != Amazon.BedrockAgent.AgentStatus.NOT_PREPARED && resp.Agent.AgentStatus != Amazon.BedrockAgent.AgentStatus.PREPARED)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 10 });
            }

            var deleteRequest = new Amazon.BedrockAgent.Model.DeleteAgentRequest()
            {
                AgentId = agent.AwsData.AgentId,
                SkipResourceInUseCheck = true,
            };

            try
            {
                var deleteResponse = await bedrockAgent.DeleteAgentAsync(deleteRequest);
                return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult() { DelayInSeconds = 10 });
            }
            catch(Exception e)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 10 });
            }
        }



        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> CreateProcessCheckAgentStatus(AwsAgent agent)
        {

            var getAgentRequest = new Amazon.BedrockAgent.Model.GetAgentRequest();
            getAgentRequest.AgentId = agent.AwsData.AgentId;
            var resp = await bedrockAgent.GetAgentAsync(getAgentRequest);
            if (resp.Agent.AgentStatus == Amazon.BedrockAgent.AgentStatus.CREATING)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 10 });
            }
            else if (resp.Agent.AgentStatus == Amazon.BedrockAgent.AgentStatus.NOT_PREPARED)
            {
                return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
            }
            else if (resp.Agent.AgentStatus == Amazon.BedrockAgent.AgentStatus.FAILED)
            {
                return (MultiStepProcessTaskRunResult.FailedAbort, new SuperControllerMultiStepResult());
            }
            else
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 30 });
            }
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> CreateProcessChecKAgentStatusRollback(AwsAgent agent)
        {

            var getAgentRequest = new Amazon.BedrockAgent.Model.GetAgentRequest();
            getAgentRequest.AgentId = agent.AwsData.AgentId;
            var resp = await bedrockAgent.GetAgentAsync(getAgentRequest);
            if (resp.Agent.AgentStatus == Amazon.BedrockAgent.AgentStatus.CREATING)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 10 });
            }
            else if (resp.Agent.AgentStatus == Amazon.BedrockAgent.AgentStatus.NOT_PREPARED || resp.Agent.AgentStatus == Amazon.BedrockAgent.AgentStatus.FAILED || resp.Agent.AgentStatus == Amazon.BedrockAgent.AgentStatus.PREPARED)
            {
                return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
            }
            else
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 10 });
            }
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.MicroserviceAuthScheme)]
        [Route(Constants.Routes.AgentController.Internal.CREATE_PROCESS)]
        [HttpPost]
        public async Task<IActionResult> CreateProcess([FromBody] MessageBusMessage message)
        {
            return await MultiStepProcess(message, GetCreationProcess());
        }
        #endregion


        //GOSTARIA MOVER PARA O CONTROLLER AgentKnowledgeBase
        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost(Constants.Routes.KnowledgeBaseController.Public.ASSIGN_KNOWLEDGEBASE)]
        public async Task<IActionResult> AssignKnowledgeBase([FromBody] AgentAssignKbRequest request)
        {
            string accountId = GetAccountId();

            var knowledgeBase = await GetDBEntry<AwsKnowledgeBase>(accountId, request.kbId);
            var agent = await GetDBEntry<AwsAgent>(accountId, request.AgentId);

            if(knowledgeBase == null ||  agent == null)
            {
                return BadRequest("Invalid Agent or Knowledgebase.");
            }

            var awsRequest = new Amazon.BedrockAgent.Model.AssociateAgentKnowledgeBaseRequest();
            awsRequest.AgentId = agent.AwsData.AgentId;
            awsRequest.KnowledgeBaseId = knowledgeBase.AwsData.AwsKbId;
            awsRequest.Description = request.Description;
            awsRequest.KnowledgeBaseState = "ENABLED";
            awsRequest.AgentVersion = "DRAFT";
            var resp = await bedrockAgent.AssociateAgentKnowledgeBaseAsync(awsRequest);

            return Ok();

        }


        #region ALIAS PROCESS
        private IMultiStepProcess<AgentStatus, AwsAgentAlias, SuperControllerMultiStepResult> GetAliasProcess()
        {
            var process = new MultiStepProcess<AgentStatus, AwsAgentAlias, SuperControllerMultiStepResult>()
                .DefineTransition(
                    AgentStatus.QUEUED,
                    AgentStatus.DB_ENTRY_CREATED,
                    new MultiStepProcessTransitionDetails<AwsAgentAlias, SuperControllerMultiStepResult>(AliasProcessCreateOrUpdateAWS, maxRetries: 10),
                    new MultiStepProcessTransitionDetails<AwsAgentAlias, SuperControllerMultiStepResult>(AliasProcessRollbackAWS, maxRetries: 10),
                    "Create or Update AWS Alias"
                )
                .DefineTransition(
                    AgentStatus.DB_ENTRY_CREATED,
                    AgentStatus.READY,
                    new MultiStepProcessTransitionDetails<AwsAgentAlias, SuperControllerMultiStepResult>(AliasProcessSaveToDB, maxRetries: 10),
                    new MultiStepProcessTransitionDetails<AwsAgentAlias, SuperControllerMultiStepResult>(AliasProcessRollbackSaveToDB, maxRetries: 10),
                    "Save Alias to Database"
                );

            return process;
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost(Constants.Routes.AgentController.Public.SET_ALIAS)]
        public async Task<IActionResult> PutAliasRequest([FromBody] AgentPutAliasRequest request)
        {
            string accountId = GetAccountId();

            AwsAgentTag agentTag = await GetDBEntry<AwsAgentTag>(request.AgentId, request.AgentTag);
            if (agentTag == null || agentTag.AccountId != accountId) return NotFound("Agent not found");

            AwsAgentAlias alias = await GetDBEntry<AwsAgentAlias>(request.AgentId, request.Alias);
            bool isUpdate = alias != null;

            if (alias == null)
            {
                alias = new AwsAgentAlias();
                alias.Alias = request.Alias;
                alias.AgentId = request.AgentId;
                alias.AgentTag = request.AgentTag;
                alias.AccountId = accountId;
                alias.Description = request.Description;
                alias.AwsData.AgentId = agentTag.AwsData.AgentId;
                alias.AwsData.AliasName = Guid.NewGuid().ToString();
                alias.AwsData.Version = agentTag.AwsData.Version;
            }
            else
            {
                alias.Description = request.Description;
            }

            alias.Status = shared.Models.Enums.AgentStatus.QUEUED;

            MessageBusDispachParams messageBusDispachParams = new MessageBusDispachParams()
            {
                Controller = Routes.AgentController.BasePath,
                Method = HttpMethod.Post,
                DelayInSeconds = 0,
                Route = Constants.Routes.AgentController.Internal.ALIAS_PROCESS,
                TargetMicroservice = MicroserviceType.Platform,
                Payload = alias
            };

            await this.DispatchApiEventV2(messageBusDispachParams);

            return Ok();
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> AliasProcessCreateOrUpdateAWS(AwsAgentAlias alias)
        {
            try
            {
                if (alias.AwsData.AliasId == null || string.IsNullOrEmpty(alias.AwsData.AliasId))
                {
                    // Create new alias
                    Amazon.BedrockAgent.Model.CreateAgentAliasRequest awsReq = new Amazon.BedrockAgent.Model.CreateAgentAliasRequest();
                    awsReq.AgentId = alias.AwsData.AgentId;
                    awsReq.AgentAliasName = alias.Alias;
                    awsReq.Description = alias.Description;
                    awsReq.RoutingConfiguration = new List<Amazon.BedrockAgent.Model.AgentAliasRoutingConfigurationListItem>() { 
                        new Amazon.BedrockAgent.Model.AgentAliasRoutingConfigurationListItem() { AgentVersion = alias.AwsData.Version } 
                    };

                    var awsResp = await bedrockAgent.CreateAgentAliasAsync(awsReq);
                    alias.AwsData.AliasId = awsResp.AgentAlias.AgentAliasId;
                }
                else
                {
                    // Update existing alias
                    var awsResp = await bedrockAgent.UpdateAgentAliasAsync(
                        new Amazon.BedrockAgent.Model.UpdateAgentAliasRequest() { 
                            AgentAliasId = alias.AwsData.AliasId,
                            AgentAliasName = alias.Alias,
                            AgentId = alias.AwsData.AgentId,
                            Description = alias.Description,
                            RoutingConfiguration = new List<Amazon.BedrockAgent.Model.AgentAliasRoutingConfigurationListItem>() { 
                                new Amazon.BedrockAgent.Model.AgentAliasRoutingConfigurationListItem() { AgentVersion = alias.AwsData.Version } 
                            } 
                        }
                    );
                    alias.AwsData.AliasId = awsResp.AgentAlias.AgentAliasId;
                }

                return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult() { ShouldUpdateDb = false });
            }
            catch (Exception ex)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 10 });
            }
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> AliasProcessRollbackAWS(AwsAgentAlias alias)
        {
            try
            {
                if (!string.IsNullOrEmpty(alias.AwsData.AliasId))
                {
                    var deleteRequest = new Amazon.BedrockAgent.Model.DeleteAgentAliasRequest()
                    {
                        AgentAliasId = alias.AwsData.AliasId,
                        AgentId = alias.AwsData.AgentId
                    };

                    await bedrockAgent.DeleteAgentAliasAsync(deleteRequest);
                }
                return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
            }
            catch (Exception ex)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 10 });
            }
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> AliasProcessSaveToDB(AwsAgentAlias alias)
        {
            if (await PutDBEntry2(alias))
            {
                return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult() { ShouldUpdateDb = false });
            }
            else
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 10 });
            }
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> AliasProcessRollbackSaveToDB(AwsAgentAlias alias)
        {
            // For rollback, we don't need to do anything special since the AWS alias was already created/updated
            return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.MicroserviceAuthScheme)]
        [Route(Constants.Routes.AgentController.Internal.ALIAS_PROCESS)]
        [HttpPost]
        public async Task<IActionResult> PutAliasProcess([FromBody] MessageBusMessage message)
        {
            return await MultiStepProcess(message, GetAliasProcess());
        }
        #endregion


        #region DEPLOY PROCESS
        private IMultiStepProcess<AgentStatus, IMultiStepProcessWrapper<AwsAgent, AgentStatus>, SuperControllerMultiStepResult> GetDeployProcess()
        {
            var process = new MultiStepProcess<AgentStatus, AgentDeployProcessRequest, SuperControllerMultiStepResult>()
                .DefineTransition(
                    AgentStatus.SEALING,
                    AgentStatus.PREPARING,
                    new MultiStepProcessTransitionDetails<AgentDeployProcessRequest, SuperControllerMultiStepResult>(DeployProcessPrepareAgent, maxRetries: 3),
                    new MultiStepProcessTransitionDetails<AgentDeployProcessRequest, SuperControllerMultiStepResult>(DeployProcessRollbackPrepareAgent, maxRetries: 3),
                    "Prepare Agent"
                )
                .DefineTransition(
                    AgentStatus.PREPARING,
                    AgentStatus.VERSIONING,
                    new MultiStepProcessTransitionDetails<AgentDeployProcessRequest, SuperControllerMultiStepResult>(DeployProcessCheckAgentStatus, maxRetries: 20),
                    new MultiStepProcessTransitionDetails<AgentDeployProcessRequest, SuperControllerMultiStepResult>(DeployProcessRollbackCheckAgentStatus, maxRetries: 20),
                    "Check Agent Status"
                )
                .DefineTransition(
                    AgentStatus.VERSIONING,
                    AgentStatus.WAITING_VERSION,
                    new MultiStepProcessTransitionDetails<AgentDeployProcessRequest, SuperControllerMultiStepResult>(DeployProcessCreateAliasAndTag, maxRetries: 10),
                    new MultiStepProcessTransitionDetails<AgentDeployProcessRequest, SuperControllerMultiStepResult>(DeployProcessRollbackCreateAliasAndTag, maxRetries: 10),
                    "Create Alias and Tag"
                )
                .DefineTransition(
                    AgentStatus.WAITING_VERSION,
                    AgentStatus.TAGGING,
                    new MultiStepProcessTransitionDetails<AgentDeployProcessRequest, SuperControllerMultiStepResult>(DeployProcessWaitForVersion, maxRetries: 20),
                    new MultiStepProcessTransitionDetails<AgentDeployProcessRequest, SuperControllerMultiStepResult>(DeployProcessRollbackWaitForVersion, maxRetries: 20),
                    "Wait for Version Assignment"
                )
                .DefineTransition(
                    AgentStatus.TAGGING,
                    AgentStatus.READY,
                    new MultiStepProcessTransitionDetails<AgentDeployProcessRequest, SuperControllerMultiStepResult>(DeployProcessSaveTag, maxRetries: 10),
                    new MultiStepProcessTransitionDetails<AgentDeployProcessRequest, SuperControllerMultiStepResult>(DeployProcessRollbackSaveTag, maxRetries: 10),
                    "Save Agent Tag"
                );

            return (IMultiStepProcess<AgentStatus, IMultiStepProcessWrapper<AwsAgent, AgentStatus>, SuperControllerMultiStepResult>)process;
        }

        

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost(Constants.Routes.AgentController.Public.DEPLOY)]
        public async Task<IActionResult> DeployAgent([FromRoute] string agentId, [FromBody] AgentDeployRequest request)
        {
            string accountId = GetAccountId();
            AwsAgent agent = await GetDBEntry<AwsAgent>(accountId, agentId);
            if (agent == null) return BadRequest("Agent not found");

            AwsAgentTag awsAgentTag = await GetDBEntry<AwsAgentTag>(agentId, request.Tag);
            if (awsAgentTag != null) return BadRequest("Tag already exists");

            var setAgent = await SetDBEntryStatusAtomic(agent, shared.Models.Enums.AgentStatus.SEALING, new List<AgentStatus>() { AgentStatus.READY });
            if (!setAgent) return BadRequest("Can only deploy an agent that is in READY state.");

            var processRequest = new AgentDeployProcessRequest();
            processRequest.Agent = agent;
            processRequest.Request = request;

            MessageBusDispachParams messageBusDispachParams = new MessageBusDispachParams()
            {
                Controller = Routes.AgentController.BasePath,
                Method = HttpMethod.Post,
                DelayInSeconds = 0,
                Route = Constants.Routes.AgentController.Internal.DEPLOY_PROCESS,
                TargetMicroservice = MicroserviceType.Platform,
                Payload = processRequest
            };

            await this.DispatchApiEventV2(messageBusDispachParams);

            return Ok();
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> DeployProcessPrepareAgent(AgentDeployProcessRequest request)
        {
            try
            {
                var preReq = new Amazon.BedrockAgent.Model.PrepareAgentRequest();
                preReq.AgentId = request.Agent.AwsData.AgentId;
                var resp = await bedrockAgent.PrepareAgentAsync(preReq);

                return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult() { ShouldUpdateDb = true });
            }
            catch (Exception ex)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 10 });
            }
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> DeployProcessRollbackPrepareAgent(AgentDeployProcessRequest request)
        {
            // No specific rollback needed for prepare agent
            return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> DeployProcessCheckAgentStatus(AgentDeployProcessRequest request)
        {
            try
            {
                var agentReq = new Amazon.BedrockAgent.Model.GetAgentRequest();
                agentReq.AgentId = request.Agent.AwsData.AgentId;
                var resp = await bedrockAgent.GetAgentAsync(agentReq);

                if (resp.Agent.AgentStatus == Amazon.BedrockAgent.AgentStatus.PREPARED)
                {
                    return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult() { ShouldUpdateDb = true });
                }
                else
                {
                    return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 20 });
                }
            }
            catch (Exception ex)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 10 });
            }
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> DeployProcessRollbackCheckAgentStatus(AgentDeployProcessRequest request)
        {
            // No specific rollback needed for status check
            return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> DeployProcessCreateAliasAndTag(AgentDeployProcessRequest request)
        {
            try
            {
                string awsAliasName = Guid.NewGuid().ToString();

                var awsReq = new Amazon.BedrockAgent.Model.CreateAgentAliasRequest();
                awsReq.AgentId = request.Agent.AwsData.AgentId;
                awsReq.AgentAliasName = awsAliasName;
                awsReq.Description = request.Agent.Description;
                awsReq.ClientToken = request.Agent.AgentId + "-" + shared.Helpers.EncodingHelper.MD5(request.Request.Tag);

                var awsResp = await bedrockAgent.CreateAgentAliasAsync(awsReq);

                string accountId = GetAccountId();

                request.Agent.DeployAgentTag = new AwsAgentTag();
                request.Agent.DeployAgentTag.Tag = request.Request.Tag;
                request.Agent.DeployAgentTag.AccountId = accountId;
                request.Agent.DeployAgentTag.AgentId = request.Agent.AgentId;
                request.Agent.DeployAgentTag.AwsData.AgentId = request.Agent.AwsData.AgentId;
                request.Agent.DeployAgentTag.AwsData.AliasId = awsResp.AgentAlias.AgentAliasId;
                request.Agent.DeployAgentTag.AwsData.Version = string.Empty;
                request.Agent.DeployAgentTag.AwsData.AliasName = awsAliasName;

                return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult() { ShouldUpdateDb = true });
            }
            catch (Exception ex)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 10 });
            }
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> DeployProcessRollbackCreateAliasAndTag(AgentDeployProcessRequest request)
        {
            try
            {
                if (request.Agent.DeployAgentTag != null && !string.IsNullOrEmpty(request.Agent.DeployAgentTag.AwsData.AliasId))
                {
                    var deleteRequest = new Amazon.BedrockAgent.Model.DeleteAgentAliasRequest()
                    {
                        AgentAliasId = request.Agent.DeployAgentTag.AwsData.AliasId,
                        AgentId = request.Agent.AwsData.AgentId
                    };

                    await bedrockAgent.DeleteAgentAliasAsync(deleteRequest);
                }
                return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
            }
            catch (Exception ex)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 10 });
            }
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> DeployProcessWaitForVersion(AgentDeployProcessRequest request)
        {
            try
            {
                var awsResp = await bedrockAgent.GetAgentAliasAsync(new Amazon.BedrockAgent.Model.GetAgentAliasRequest() { 
                    AgentAliasId = request.Agent.DeployAgentTag.AwsData.AliasId, 
                    AgentId = request.Agent.AwsData.AgentId });

                if (awsResp.AgentAlias.RoutingConfiguration[0].AgentVersion == null)
                {
                    return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 5 });
                }
                else
                {
                    request.Agent.DeployAgentTag.AwsData.Version = awsResp.AgentAlias.RoutingConfiguration[0].AgentVersion;
                    return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult() { ShouldUpdateDb = true });
                }
            }
            catch (Exception ex)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 5 });
            }
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> DeployProcessRollbackWaitForVersion(AgentDeployProcessRequest reques)
        {
            // No specific rollback needed for waiting for version
            return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> DeployProcessSaveTag(AgentDeployProcessRequest request)
        {
            try
            {
                if (await PutDBEntry2(request.Agent.DeployAgentTag))
                {
                    return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult() { ShouldUpdateDb = true });
                }
                else
                {
                    return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 10 });
                }
            }
            catch (Exception ex)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 10 });
            }
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> DeployProcessRollbackSaveTag(AgentDeployProcessRequest reques)
        {
            // For rollback, we could delete the agent tag from DB if needed
            return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> DeployProcessFinalUpdate(AgentDeployProcessRequest reques)
        {
            try
            {
                // This is the final step to ensure agent status is READY
                return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult() { ShouldUpdateDb = true });
            }
            catch (Exception ex)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 10 });
            }
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> DeployProcessRollbackFinalUpdate(AgentDeployProcessRequest request)
        {
            // No specific rollback needed for final update
            return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.MicroserviceAuthScheme)]
        [HttpPost(Constants.Routes.AgentController.Internal.DEPLOY_PROCESS)]
        public async Task<IActionResult> DeployAgentProcess([FromBody] MessageBusMessage message)
        {
            return await WrappedMultiStepProcess<AgentStatus, AwsAgent>(message, GetDeployProcess());
        }
        #endregion


        #region SEARCH
        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.AgentController.Public.SEARCH)]
        public async Task<IActionResult> SearchAgent([FromQuery] AgentSearchRequest request)
        {

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            string accountId = GetAccountId();
            List<string> propertiesToGet = typeof(AgentResponse).GetProperties().Select((PropertyInfo p) => p.Name).ToList();

            var agents = await SearchDBEntries<shared.Models.Documents.DynamoDB.Agent>(nameof(shared.Models.Documents.DynamoDB.Agent.AccountId), accountId, request.Query, request.Limit, attributesToGet: propertiesToGet);

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<shared.Models.Documents.DynamoDB.Agent, AgentResponse>();
            }, loggerFactory).CreateMapper();


            var result = new ListResponse<AgentResponse>()
            {
                Entries = mapper.Map<IList<shared.Models.Documents.DynamoDB.Agent>, IList<AgentResponse>>(agents.Entries).ToList(),
                NextToken = agents.NextToken,
                Total = agents.Total,
            };

            return Ok(result);
        }
        #endregion


        #region LIST
        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.AgentController.Public.LIST)]
        public async Task<IActionResult> ListAgents([FromQuery] AgentListRequest request)
        {
            if(!ModelState.IsValid) return BadRequest(ModelState);


            var listEntriesInternal = await GetDBEntries2<AwsAgent>(GetAccountId(), request.Count, request.Next);

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<AwsAgent, AgentResponse>();
            }, loggerFactory).CreateMapper();
             

            var result = new ListResponse<AgentResponse>()
            {
                Entries = mapper.Map<IList<AwsAgent>, IList<AgentResponse>>(listEntriesInternal.Entries).ToList(),
                NextToken = listEntriesInternal.NextToken,
                Total = listEntriesInternal.Total,
            };

            return Ok(result);
        }
        #endregion


        #region UPDATE
        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPut(Constants.Routes.AgentController.Public.PUT)]
        public async Task<IActionResult> PutAgent([FromRoute] string agentId, [FromBody] AgentPutRequest agentPutRequest)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);

            string accountId = GetAccountId();
            AwsAgent? agent = await GetDBEntry2<AwsAgent>(accountId, agentId);
            if (agent == null) return BadRequest("Not found");

            agent.Name = agentPutRequest.Name;
            agent.Description = agentPutRequest.Description;
            agent.DefaultPrompt = agentPutRequest.DefaultPrompt;

            if (!await UpdateDBEntry2<AwsAgent>(agent, new List<string>() { nameof(AwsAgent.Name), nameof(AwsAgent.Description), nameof(AwsAgent.DefaultPrompt) }))
            {
                return StatusCode(500, "Couldn't update");
            }
            return Ok(agent);
        }
        #endregion


        #region GET
        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.AgentController.Public.GET)]
        public async Task<IActionResult> GetAgent([FromRoute] string agentId)
        {
            string accountId = GetAccountId();
            AwsAgent? agent = await GetDBEntry2<AwsAgent>(accountId, agentId);
            if (agent == null) return BadRequest("Not found");

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<AwsAgent, AgentResponse>();
            }, loggerFactory).CreateMapper();

            return Ok(mapper.Map<AwsAgent, AgentResponse>(agent));
        }
        #endregion


        #region KNOWLEDGE BASES FOR AGENT
        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.AgentController.Public.GET_KNOWLEDGE_BASES_FOR_AGENT)]
        public async Task<IActionResult> GetKnowledgeBasesForAgent(string agentId, [FromQuery] AgentListKnowledgeBases request)
        {
            if (!ModelState.IsValid) { return BadRequest(ModelState); }


            var joinConfig = new DBJoinConfig<AgentKnowledgeBase, KnowledgeBase>
            {
                FirstTable = new TableQueryConfig
                {
                    HashKeyValue = GetAccountId(),
                    AdditionalFilterExpression = new Amazon.DynamoDBv2.DocumentModel.Expression()
                    {
                        ExpressionAttributeNames = new Dictionary<string, string>
                            {
                                { $"#{nameof(AgentKnowledgeBase.AgentId)}", nameof(AgentKnowledgeBase.AgentId) }
                            },
                        ExpressionAttributeValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry>
                            {
                                { $":{nameof(AgentKnowledgeBase.AgentId)}", agentId }
                            },
                        ExpressionStatement = $"#{nameof(AgentKnowledgeBase.AccountId)}=:{nameof(AgentKnowledgeBase.AccountId)}"
                    }
                },
                SecondTable = new TableQueryConfig
                {
                    HashKeyValue = GetAccountId(),
                    Index = null
                },
                JoinCondition = new JoinCondition
                {
                    FirstTableField = nameof(AgentKnowledgeBase.KnowledgebaseId),
                    SecondTableField = nameof(KnowledgeBase.KbId)
                },
                TargetResultCount = request.Count
            };


            // Perform the join
            var joinResult = await GetDBJoin(joinConfig, request.Next);

            // Extract just the KnowledgeBase entries for the response
            var knowledgeBases = joinResult.Entries.Select(entry => entry.Second).ToList();

            var response = new ListResponse<KnowledgeBase>
            {
                Entries = knowledgeBases,
                NextToken = joinResult.NextToken,
                Total = joinResult.Total
            };

            return Ok(response);
        }
        #endregion


        #region LIST TAGS
        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.AgentController.Public.LIST_TAGS)]
        public async Task<IActionResult> ListAgentTags([FromRoute] string agentId, [FromQuery] AgentListTagsRequest request)
        {
            if(!ModelState.IsValid) return BadRequest(ModelState);


            var listEntriesInternal = await GetDBEntries2<AgentTag>(agentId, request.Count, request.Next, AgentTag.AgentIdTagIdIndex);

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<AgentTag, AgentTagResponse>();
            }, loggerFactory).CreateMapper();


            var result = new ListResponse<AgentTagResponse>()
            {
                Entries = mapper.Map<IList<AgentTag>, IList<AgentTagResponse>>(listEntriesInternal.Entries).ToList(),
                NextToken = listEntriesInternal.NextToken,
                Total = listEntriesInternal.Total,
            };

            return Ok(result);
        }
        #endregion


        #region LIST ALIASES FOR AGENT
        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.AgentController.Public.LIST_ALIASES_FOR_AGENT)]
        public async Task<IActionResult> ListAliasesForAgent([FromRoute] string agentId, [FromQuery] AgentListAliasesRequest request)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);

            var accountId = GetAccountId();
            var listEntriesInternal = await GetDBEntries2<shared.Models.Documents.DynamoDB.AgentAlias>(
                agentId,
                request.Count,
                request.Next,
                AgentAlias.AgentIdAliasIndex);

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<shared.Models.Documents.DynamoDB.AgentAlias, AgentAliasResponse>();
            }, loggerFactory).CreateMapper();

            var result = new ListResponse<AgentAliasResponse>()
            {
                Entries = mapper.Map<IList<shared.Models.Documents.DynamoDB.AgentAlias>, IList<AgentAliasResponse>>(listEntriesInternal.Entries).ToList(),
                NextToken = listEntriesInternal.NextToken,
                Total = listEntriesInternal.Total,
            };

            return Ok(result);
        }
        #endregion

    }
}
