﻿
using System.Net.NetworkInformation;

namespace platform.Constants
{
    public static class Routes
    {

        public static class AgentAliasController
        {
            public const string BasePath = "agentalias";
            public static class Public
            {
                public const string LIST = "";
                public const string LIST_FOR_AGENT_ID = "{agentId}";
            }
        }
        public static class AgentKnowledgebaseController
        {
            public const string BasePath = "agentkb";
            public static class Public
            {
                public const string ASSOC_EXISTS = "exists";
                public const string ASSIGN = "assign";
            }

            public static class Internal
            {
                public const string ASSIGN = "assign_internal";
            }
        }
        public static class APIKeyController
        {
            public const string BasePath = "apikey";
            public static class Public
            {
                public const string LIST = "";
            }
        }

        public static class SigninController
        {
            public const string BasePath = "Signin";
            public static class Public
            {
                public const string UPDATE_TOKEN = "update";
            }
        }

        
        public static class AgentBehaviorTreeController
        {
            public const string BasePath = "agentbehaviortree";
            public static class Public
            {
                public const string LIST = "";
                public const string GET = "{btId}";
            }
        }
        

        public static class SignupController
        {
            public static class Internal
            {
                public const string SIGNUP_PROCESS = "internal/signup_process/";
            }
            public static class Public
            {
                public const string SIGNUP_STATUS = "status/{requestId}";
                public const string CONFIRM_EMAIL = "confirmemail";
                public const string RESEND_CONFIRMATION_EMAIL = "resendconfirmation";
            }
            public const string BasePath = "Signup";
        }

        public static class KnowledgeBaseController
        {
            public static class Internal
            {
                public const string CREATION_PROCESS = "internal/kb_creation_process/";
                public const string DEPLOY_PROCESS = "internal/kb_deploy_process/";
            }
            public static class Public
            {
                public const string ASSIGN_KNOWLEDGEBASE = "assignkb";
                public const string DEPLOY = "{kbId}/deploy";
                public const string PUT = "{kbId}";
                public const string GET_KNOWLEDGEBASE = "{kbId}";
                public const string GET_AGENTS_FOR_KNOWLEDGE_BASE = "{kbId}/agents";
            }
            public const string BasePath = "KnowledgeBase";
        }

        public static class AgentController
        {
            public static class Internal
            {
                public const string CREATE_PROCESS = "internal/agent_create_process/";
                public const string DEPLOY_PROCESS = "internal/agent_deploy_process/";
                public const string ALIAS_PROCESS = "internal/agent_alias_process/";
            }
            public static class Public {
                public const string DEPLOY = "{agentId}/deploy";
                public const string GET = "{agentId}";
                public const string SET_ALIAS = "alias";
                public const string SEARCH = "search";
                public const string LIST = "";
                public const string LIST_TAGS = "{agentId}/tags";
                public const string PUT = "{agentId}";
                public const string GET_KNOWLEDGE_BASES_FOR_AGENT = "{agentId}/knowledgebases";
                public const string CREATE_ALIAS = "{agentId}/alias";
                public const string LIST_ALIASES_FOR_AGENT = "{agentId}/alias";
            }
            public const string BasePath = "Agent";
        }

        public static class KnowledgeBaseFileController
        {
            public static class Internal
            {
            }
            public static class Public
            {
                public const string UPLOAD = "{kbId}/upload";
                public const string LIST_FILES_FOR_KNOWLEDGEBASE = "";
                public const string DOWNLOAD = "{fileId}/download";
                public const string DELETE_MULTIPLE = "";
            }
            public const string BasePath = "kbFiles";
        }

        
    }
    
    
}
