﻿using Amazon.DynamoDBv2.DataModel;
using shared.Models.Interfaces;

namespace shared.Models.Documents.DynamoDB
{
    [DynamoDBTable(nameof(AgentTag))]
    public class AgentTag : DynamoDBModel, INoSQLStatic
    {
        public const string AgentIdTagIdIndex = "AgentIdTagIdIndex";

        [DynamoDBHashKey]
        public string AccountId { get; set; } = string.Empty;

        [DynamoDBGlobalSecondaryIndexHashKey(AgentIdTagIdIndex)]
        public string AgentId { get; set; } = string.Empty;

        [DynamoDBRangeKey]
        [DynamoDBGlobalSecondaryIndexRangeKey(AgentIdTagIdIndex)]
        public string Tag { get; set; } = string.Empty;
        

        /// <summary>
        /// Gets the hash key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public static new string GetHashKeyPropertyName(string? indexName = null)
        {
            return nameof(AgentTag.AgentId);
        }

        /// <summary>
        /// Gets the range key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            return nameof(AgentTag.Tag);
        }

        /// <summary>
        /// Gets the table name from DynamoDBTable attribute.
        /// </summary>
        /// <returns>Table name</returns>
        public static new string GetTableName()
        {
            return nameof(AgentTag);
        }
    }
}
