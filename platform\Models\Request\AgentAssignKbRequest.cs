using Amazon.DynamoDBv2.DataModel;
using System.ComponentModel.DataAnnotations;

namespace platform.Models.Request
{
    public class AgentAssignKbRequest
    {
        [Required]
        public string AgentId { get; set; } = string.Empty;

        [Required]
        public string kbId { get; set; } = string.Empty;

        [Required]
        [MinLength(10)]
        public string Description {  get; set; } = string.Empty;
    }
}
