﻿using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using shared.Components.ApiEventBus;
using shared.Components.MessageBus.Interfaces;
using shared.Components.MessageBus.Models;
using shared.Components.MultiStepProcess.Enums;
using shared.Components.MultiStepProcess.Interfaces;
using shared.Components.MultiStepProcess.Models;
using shared.Converters;
using shared.Models.Documents;
using shared.Models.Documents.DynamoDB;
using shared.Models.Enums;
using shared.Models.Interfaces;
using shared.Models.Response;
using shared.Services;
using Stateless;
using System.Security.Claims;
using System.Text;

namespace shared.Controllers
{
    public class SuperController2 : Controller
    {
        protected readonly IMessageBus messageBus;
        protected readonly IDynamoDBContext dBContext;
        protected readonly IUniqueIDService uniqueIDService;

        public SuperController2(IDynamoDBContext dBContext, IMessageBus messageBus, IUniqueIDService uniqueIDService)
        {
            this.uniqueIDService = uniqueIDService;
            this.dBContext = dBContext;
            this.messageBus = messageBus;
        }


        #region MESSAGE BUS
            protected async Task<MessageBusResult> DispatchApiEventV2(MessageBusDispachParams dispatch)
            {
                List<Claim> claims = new List<Claim> { new Claim("AccountId", GetAccountId()) };

                try
                {
                    var request = new MessageBusSendRequest<object>(dispatch.Payload, dispatch.TargetMicroservice, dispatch.Controller, dispatch.Route);
                    request.DelayInSeconds = dispatch.DelayInSeconds;
                    request.Claims = claims;

                    return await messageBus.SendAsync(request);
                }
                catch (Exception ex)
                {
                    return MessageBusResult.Error;
                }
            }

            protected async Task<MessageBusResult> RequeueV2(MessageBusMessage message)
            {
                return await messageBus.Requeue(message);
            }
            #endregion


        #region MULTI STEP PROCESS
        protected class SuperControllerMultiStepResult
            {
                public bool ShouldUpdateDb { get; set; } = true;
                public int DelayInSeconds { get; set; } = 3;
            }

            protected async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> MultiStepProcessDummyTask(object any)
            {
                return await Task.FromResult((MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult()));
            }

            protected async Task<IActionResult> MultiStepProcess<TState, TObject>(MessageBusMessage message, IMultiStepProcess<TState, TObject, SuperControllerMultiStepResult> process) where TState : struct where TObject : DynamoDBModel, IStateful<TState>
            {
                var stateData = message.GetPayload<MultiStepProcessStateData<TState, TObject>>();
                if (stateData == null)
                {
                    return BadRequest();
                }
                process.LoadStateData(stateData);

                bool shouldUpdateDb = true;
                MessageBusDispachParams nextDispatch = new MessageBusDispachParams(message);
                nextDispatch.DelayInSeconds = 10*message.RetryCount;

                bool shouldRequeue = false;

                //missing a save only
                if (!process.IsFinished)
                {
                    var iterationResult = await process.ExecuteIteration();

                    if(iterationResult.Result != null)
                    {
                        nextDispatch.DelayInSeconds = iterationResult.Result.DelayInSeconds;
                        shouldUpdateDb = iterationResult.Result.ShouldUpdateDb;
                    }
                

                    if (!iterationResult.Success)
                    {
                        switch (process.FailureReason)
                        {
                            case Components.MultiStepProcess.Enums.MultiStepProcessFailureReason.Timeout:
                                shouldRequeue = true;
                                break;
                            case Components.MultiStepProcess.Enums.MultiStepProcessFailureReason.RollbackFailed:
                            case Components.MultiStepProcess.Enums.MultiStepProcessFailureReason.RetryLimitExceeded:
                            case Components.MultiStepProcess.Enums.MultiStepProcessFailureReason.TaskAborted:
                            case Components.MultiStepProcess.Enums.MultiStepProcessFailureReason.InvalidTransition:
                            case Components.MultiStepProcess.Enums.MultiStepProcessFailureReason.InvalidConfiguration:
                                return StatusCode(500);
                        }
                    
                    }
                }

                bool updatedDb = true;
                if (shouldUpdateDb) updatedDb = await PutDBEntry2(stateData.StatefulObject);

                if(process.IsFinished && !updatedDb && shouldUpdateDb && nextDispatch != null)
                {
                    await DispatchApiEventV2(nextDispatch);
                }else if (!process.IsFinished && nextDispatch != null)
                {
                    await DispatchApiEventV2(nextDispatch);
                }else if (shouldRequeue)
                {
                    await RequeueV2(message);
                }

                switch(await messageBus.ConfirmMessage(message))
                {
                    case MessageBusResult.Error:
                    case MessageBusResult.TooManyRetries:
                        Console.WriteLine("Failed to confirm message to the bus, will duplicate");
                        break;
                }
                return Ok();
            
            }

            #endregion


        #region NoSQL Operations
        protected async Task<ListResponse<T>> GetDBEntries2<T>(
            string hashKeyValue, 
            int limit, 
            string? previousToken = null, 
            string? index = null, 
            List<string>? attributesToGet = null, 
            Amazon.DynamoDBv2.DocumentModel.Expression? filterExpression = null) where T : INoSQLModel
        {
            string hashKeyName = T.GetHashKeyPropertyName(index);
            int totalElements = -1;
            var config = new QueryOperationConfig()
            {
                Limit = 0,
                KeyExpression = new Amazon.DynamoDBv2.DocumentModel.Expression()
                {
                    ExpressionAttributeNames = new Dictionary<string, string>() { { $"#{hashKeyName}", hashKeyName } },
                    ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { $":{hashKeyName}", hashKeyValue } },
                    ExpressionStatement = $"#{hashKeyName}=:{hashKeyName}"
                },
                FilterExpression = filterExpression,
                BackwardSearch = true
            };
            if (index != null) config.IndexName = index;

            if (previousToken == null) {
                config.Select = SelectValues.Count;
                var table = dBContext.GetTargetTable<T>();
                var countSearch = table.Query(config).GetRemainingAsync();
                totalElements = (await countSearch).Count;

                if (totalElements <= 0)
                {
                    return new ListResponse<T>() { Entries = new List<T>(), NextToken = null, Total = totalElements };
                }
            }
            else
            {
                config.PaginationToken = previousToken;
            }


            if (attributesToGet != null)
            {
                config.AttributesToGet = attributesToGet;
                config.Select = SelectValues.SpecificAttributes;
            }
            else
            {
                config.Select = SelectValues.AllAttributes;
            }

            config.Limit = limit;

            var search = dBContext.FromQueryAsync<T>(config);
            List<T> entries = new List<T>();
            entries.AddRange(await search.GetNextSetAsync());
            return new ListResponse<T>() { Entries = entries, NextToken = search.PaginationToken, Total = totalElements };
        }


        protected async Task<T?> GetDBEntry2<T>(string hashKey, string? rangeKey = null, string? index = null) where T : DynamoDBModel, INoSQLModel
        {

            if(index == null)
            {
                if(rangeKey == null)
                {
                    return await dBContext.LoadAsync<T>(hashKey);
                }
                else
                {
                    return await dBContext.LoadAsync<T>(hashKey, rangeKey);
                }
            }
            else
            {
                if(rangeKey == null) throw new ArgumentNullException(nameof(rangeKey));
                var rangekeyPropertyName = T.GetRangeKeyPropertyName(index);
                var queryConfig = new QueryConfig()
                {
                    IndexName = index,
                    QueryFilter = new List<ScanCondition>() {
                            {
                                new ScanCondition(
                                    rangekeyPropertyName,
                                    ScanOperator.Equal,
                                    [rangeKey]
                                )
                            }
                    }
                };
                var search = dBContext.QueryAsync<T>(hashKey, queryConfig);
                var entities = await search.GetRemainingAsync();
                if (entities.Count != 1) return null;
                return entities[0];
            }

        }

        protected async Task<bool> PutDBEntry2<T>(T entity) where T : DynamoDBModel
        {
            try
            {
                entity.DataVersion += 1;
                entity.LastChangeTimestamp = CurrentTimestamp();
                entity.SearchString = entity.GetSearchString();
                await dBContext.SaveAsync(entity);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        protected async Task<bool> DeleteDBEntry2<T>(T item) where T : DynamoDBModel
        {
            try
            {
                var hashKeyValue = item.GetHashKeyValue();
                if (string.IsNullOrEmpty(hashKeyValue))
                {
                    return false;
                }

                var rangeKeyValue = item.GetRangeKeyValue();

                if (!string.IsNullOrEmpty(rangeKeyValue))
                {
                    // Model has both hash and range key
                    await dBContext.DeleteAsync<T>(hashKeyValue, rangeKeyValue);
                }
                else
                {
                    // Model has only hash key
                    await dBContext.DeleteAsync<T>(hashKeyValue);
                }

                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }


        protected async Task<bool> UpdateDBEntry2<T>(
            T item,
            List<string> fields,
            bool atomic = true) where T : DynamoDBModel
        {

            var conditionExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            if (atomic)
            {
                conditionExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#D", "DataVersion" } };
                conditionExpression.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { ":d", item.DataVersion } };
                conditionExpression.ExpressionStatement = "#D=:d";
            }

            var expressionAttributeNames = new Dictionary<string, string>() { { "#D", nameof(DynamoDBModel.DataVersion) }, { "#T", nameof(DynamoDBModel.LastChangeTimestamp) }, { "#S", nameof(DynamoDBModel.SearchString) } };
            var expressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { ":dp1", item.DataVersion + 1 }, { ":t", CurrentTimestamp() }, { ":s", item.GetSearchString() } };
            var statements = new List<string>() { "#D=:dp1", "#T=:t", "#S=:s" };
            foreach (string field in fields)
            {
                Type? entryConverter = item.GetDynamoDBConverter(field);
                IPropertyConverter? converter = null;
                if (entryConverter != null)
                {
                    converter = Activator.CreateInstance(entryConverter) as IPropertyConverter;
                }
                string value = (converter == null) ? item[field]?.ToString() ?? "" : converter.ToEntry(item[field]);
                statements.Add($"#{field}=:{field}");
                expressionAttributeValues.Add($":{field}", value);
                expressionAttributeNames.Add($"#{field}", field);
            }

            var updateExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            updateExpression.ExpressionAttributeNames = expressionAttributeNames;
            updateExpression.ExpressionAttributeValues = expressionAttributeValues;
            updateExpression.ExpressionStatement = "set " + String.Join(", ", statements);


            var tw = dBContext.CreateTransactWrite<T>();
            string? hashKey = item.GetHashKeyValue();
            string? rangeKey = item.GetRangeKeyValue();
            if (hashKey == null) throw new Exception("Document is missing a hash key");

            if (rangeKey == null)
                tw.AddSaveItem(hashKey, updateExpression, conditionExpression);
            else
                tw.AddSaveItem(hashKey, rangeKey, updateExpression, conditionExpression);

            try
            {
                await tw.ExecuteAsync();
                await dBContext.LoadAsync(item);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        #endregion












































        protected async Task<T?> UpdateDBEntry<T>(
            T item,
            List<string> fields,
            bool atomic = true) where T : DynamoDBModel
        {

            var conditionExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            if (atomic)
            {
                conditionExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#D", "DataVersion" } };
                conditionExpression.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { ":d", item.DataVersion } };
                conditionExpression.ExpressionStatement = "#D=:d";
            }

            var expressionAttributeNames = new Dictionary<string, string>() { { "#D", nameof(DynamoDBModel.DataVersion) }, { "#T", nameof(DynamoDBModel.LastChangeTimestamp) }, { "#S", nameof(DynamoDBModel.SearchString) } };
            var expressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { ":dp1", item.DataVersion + 1 }, { ":t", CurrentTimestamp() }, { ":s", item.GetSearchString() } };
            var statements = new List<string>() { "#D=:dp1", "#T=:t", "#S=:s" };
            foreach (string field in fields)
            {
                Type? entryConverter = item.GetDynamoDBConverter(field);
                IPropertyConverter? converter = null;
                if (entryConverter != null) {
                    converter = Activator.CreateInstance(entryConverter) as IPropertyConverter; 
                }
                string value = (converter == null) ? item[field].ToString() ?? "" : converter.ToEntry(item[field]);
                statements.Add($"#{field}=:{field}");
                expressionAttributeValues.Add($":{field}", value);
                expressionAttributeNames.Add($"#{field}", field);
            }

            var updateExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            updateExpression.ExpressionAttributeNames = expressionAttributeNames;
            updateExpression.ExpressionAttributeValues = expressionAttributeValues;
            updateExpression.ExpressionStatement = "set " + String.Join(", ", statements);


            var tw = dBContext.CreateTransactWrite<T>();
            string? hashKey = item.GetHashKeyValue();
            string? rangeKey = item.GetRangeKeyValue();
            if (hashKey == null) throw new Exception("Document is missing a hash key");

            if (rangeKey == null)
                tw.AddSaveItem(hashKey, updateExpression, conditionExpression);
            else
                tw.AddSaveItem(hashKey, rangeKey, updateExpression, conditionExpression);

            try
            {
                await tw.ExecuteAsync();
                return await dBContext.LoadAsync(item);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        protected async Task<ListResponse<T>> SearchDBEntries<T>(
            string hashKeyProperty, string hashKeyValue, string search, int items, string? previousToken = null, List<string>? attributesToGet = null, bool getTotal = true)
        where T : DynamoDBModel { 
            Amazon.DynamoDBv2.DocumentModel.Expression filterExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            filterExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#S", nameof(DynamoDBModel.SearchString) } };
            filterExpression.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { ":s", search } };
            filterExpression.ExpressionStatement = $"contains(#S, :s)";

            return await GetDBEntries<T>(hashKeyProperty, hashKeyValue, items, previousToken, attributesToGet: attributesToGet, filterExpression: filterExpression, getTotal: getTotal);
        }

            protected async Task<ListResponse<T>> GetDBEntries<T>(
            string hashKeyName, string hashKeyValue, int items, string? previousToken = null, string? index = null, List<string>? attributesToGet = null, Amazon.DynamoDBv2.DocumentModel.Expression? filterExpression = null, bool getTotal = true)
        {

            int totalElements = -1;

            var config = new QueryOperationConfig()
            {
                Limit = 1000,
                KeyExpression = new Amazon.DynamoDBv2.DocumentModel.Expression()
                {
                    ExpressionAttributeNames = new Dictionary<string, string>() { { $"#{hashKeyName}", hashKeyName } },
                    ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { $":{hashKeyName}", hashKeyValue } },
                    ExpressionStatement = $"#{hashKeyName}=:{hashKeyName}"
                },
                FilterExpression = filterExpression,
                BackwardSearch = true
            };

            if (index != null) config.IndexName = index;

            if (getTotal)
            {
                config.Select = SelectValues.Count;
                var table = dBContext.GetTargetTable<T>();
                var countSearch = table.Query(config).GetRemainingAsync();
                totalElements = (await countSearch).Count;
            }
            else{
                config.PaginationToken = previousToken;
            }
            
            if(attributesToGet != null)
            {
                config.AttributesToGet = attributesToGet;
                config.Select = SelectValues.SpecificAttributes;
            }
            else
            {
                config.Select = SelectValues.AllAttributes;
            }

            


            var search = dBContext.FromQueryAsync<T>(config);
            List<T> entries = new List<T>();
            if (items < 0) {
                entries.AddRange(await search.GetRemainingAsync());
            }
            else
            {
                entries.AddRange(await search.GetNextSetAsync());
            }
            

            return new ListResponse<T>() { Entries = entries, NextToken = search.PaginationToken, Total = totalElements };
        }

        protected async Task<ListResponse<T>> GetDBEntriesByGSI<T>(
            string indexName,
            string hashKeyName,
            string hashKeyValue,
            string rangeKeyName,
            string rangeKeyValue,
            int items,
            string? previousToken = null,
            List<string>? attributesToGet = null,
            Amazon.DynamoDBv2.DocumentModel.Expression? filterExpression = null,
            bool getTotal = true)
        where T : DynamoDBModel
        {
            int totalElements = -1;

            var config = new QueryOperationConfig()
            {
                Limit = 1000,
                IndexName = indexName,
                KeyExpression = new Amazon.DynamoDBv2.DocumentModel.Expression()
                {
                    ExpressionAttributeNames = new Dictionary<string, string>()
                    {
                        { $"#{hashKeyName}", hashKeyName },
                        { $"#{rangeKeyName}", rangeKeyName }
                    },
                    ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>()
                    {
                        { $":{hashKeyName}", hashKeyValue },
                        { $":{rangeKeyName}", rangeKeyValue }
                    },
                    ExpressionStatement = $"#{hashKeyName}=:{hashKeyName} AND #{rangeKeyName}=:{rangeKeyName}"
                },
                FilterExpression = filterExpression,
                BackwardSearch = true
            };

            if (getTotal)
            {
                config.Select = SelectValues.Count;
                var table = dBContext.GetTargetTable<T>();
                var countSearch = table.Query(config).GetRemainingAsync();
                totalElements = (await countSearch).Count;
            }
            else
            {
                config.PaginationToken = previousToken;
            }

            if (attributesToGet != null)
            {
                config.AttributesToGet = attributesToGet;
                config.Select = SelectValues.SpecificAttributes;
            }
            else
            {
                config.Select = SelectValues.AllAttributes;
            }

            var search = dBContext.FromQueryAsync<T>(config);
            List<T> entries = new List<T>();
            if (items < 0)
            {
                entries.AddRange(await search.GetRemainingAsync());
            }
            else
            {
                entries.AddRange(await search.GetNextSetAsync());
            }

            return new ListResponse<T>() { Entries = entries, NextToken = search.PaginationToken, Total = totalElements };
        }

        protected async Task<T?> SetDBEntryStatusAtomic<T, S>(T item, S newStatus, List<S> oldStatuses, string statusFieldName = "Status") where T : DynamoDBModel where S : struct, IConvertible
        {
            var statement = new StringBuilder("#D=:d");
            var enumConvert = new DynamoEnumStringConverter<S>();
            var updateExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            updateExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#S", statusFieldName }, { "#D", "DataVersion" } };
            updateExpression.ExpressionAttributeValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry>() { { ":s", enumConvert.ToEntry(newStatus) }, { ":dp1", item.DataVersion+1 } };
            updateExpression.ExpressionStatement = "#S=:s, #D=:dp1";

            var conditionExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            conditionExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#St", statusFieldName }, { "#D", "DataVersion" } };
            conditionExpression.ExpressionAttributeValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry>() { { ":d", item.DataVersion } };
            
            var n = 0;
            foreach (S status in oldStatuses)
            {
                conditionExpression.ExpressionAttributeValues.Add($":s{n}", enumConvert.ToEntry(status));
                statement.Append($"#St=:s{n} OR");
            }
            statement = statement.Remove(statement.Length - 3, 3);
            

            var tw = dBContext.CreateTransactWrite<T>();
            string? hashKey = item.GetHashKeyValue();
            string? rangeKey = item.GetRangeKeyValue();
            if (hashKey == null) throw new Exception("Document is missing a hash key");

            if (rangeKey == null)
                tw.AddSaveItem(hashKey, updateExpression, conditionExpression);
            else
                tw.AddSaveItem(hashKey, rangeKey, updateExpression, conditionExpression);

            try
            {
                await tw.ExecuteAsync();
                return await dBContext.LoadAsync(item);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        protected async Task<T?> SetDBEntryStatusAtomic<T, S>(T item, S newStatus, S? oldStatus = null, string statusFieldName = "Status") where T : DynamoDBModel where S : struct, IConvertible
        {
            var enumConvert = new DynamoEnumStringConverter<S>();
            var updateExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            updateExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#S", statusFieldName }, { "#D", "DataVersion" } };
            updateExpression.ExpressionAttributeValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry>() { { ":s", enumConvert.ToEntry(newStatus) }, { ":dp1", item.DataVersion + 1 } };
            updateExpression.ExpressionStatement = "set #S=:s, #D=:dp1";

            var conditionExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            conditionExpression.ExpressionStatement = "#D=:d";
            conditionExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#D", "DataVersion" } };
            conditionExpression.ExpressionAttributeValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry>() { { ":d", item.DataVersion } };

            if (oldStatus != null)
            {
                conditionExpression.ExpressionStatement = conditionExpression.ExpressionStatement + " and #St=:st1";
                conditionExpression.ExpressionAttributeNames.Add("#St", statusFieldName);
                conditionExpression.ExpressionAttributeValues.Add(":st1", enumConvert.ToEntry(oldStatus));
            }

            var tw = dBContext.CreateTransactWrite<T>();
            string? hashKey = item.GetHashKeyValue();
            string? rangeKey = item.GetRangeKeyValue();
            if (hashKey == null) throw new Exception("Document is missing a hash key");

            if(rangeKey == null)
                tw.AddSaveItem(hashKey, updateExpression, conditionExpression);
            else
                tw.AddSaveItem(hashKey, rangeKey, updateExpression, conditionExpression);

            try
            {
                await tw.ExecuteAsync();
                return await dBContext.LoadAsync(item);
            } catch (Exception ex) {
                return null;
            }

        }

        protected async Task<T> PutDBEntry<T>(T entity) where T : DynamoDBModel
        {
            try
            {
                entity.DataVersion += 1;
                entity.LastChangeTimestamp = CurrentTimestamp();
                entity.SearchString = entity.GetSearchString();
                await dBContext.SaveAsync(entity);
                return entity;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        

        protected async Task<bool> DeleteDBEntry<T>(string hashKey, bool notFoundIsGood = false) where T : DynamoDBModel
        {
            try
            {
                await dBContext.DeleteAsync<T>(hashKey);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        protected async Task<bool> DeleteDBEntry<T>(string hashKey, string rangeKey, string? indexName = null, bool notFoundIsGood = false) where T : DynamoDBModel
        {
            var config = new DynamoDBOperationConfig();
            if (indexName != null) config.IndexName = indexName;
            try
            {
                await dBContext.DeleteAsync<T>(hashKey, rangeKey, config);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        protected async Task<bool> DeleteDBEntry<T>(T item, bool notFoundIsGood = false) where T : DynamoDBModel
        {
            try
            {
                var hashKeyValue = item.GetHashKeyValue();
                if (string.IsNullOrEmpty(hashKeyValue))
                {
                    return false;
                }

                var rangeKeyValue = item.GetRangeKeyValue();

                if (!string.IsNullOrEmpty(rangeKeyValue))
                {
                    // Model has both hash and range key
                    await dBContext.DeleteAsync<T>(hashKeyValue, rangeKeyValue);
                }
                else
                {
                    // Model has only hash key
                    await dBContext.DeleteAsync<T>(hashKeyValue);
                }

                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        protected async Task<List<T>> BatchGetDBEntry<T>(string hashKey, List<string> rangeKeys) where T : DynamoDBModel
        {
            var br = dBContext.CreateBatchGet<T>();
            rangeKeys.ForEach(e => br.AddKey(hashKey, e));

            try
            {
                await br.ExecuteAsync();
                return br.Results;
            }
            catch (Exception ex)
            {
                return new List<T>();
            }
        }

        protected async Task<List<T>> BatchGetDBEntry<T>(string hashKey, List<string> rangeKeys, string indexName, string rangeKeyName) where T : DynamoDBModel
        {
            var config = new DynamoDBOperationConfig();
            var search = dBContext.QueryAsync<T>(hashKey,
                new DynamoDBOperationConfig()
                {
                    IndexName = indexName,
                    QueryFilter = new List<ScanCondition>() {
                            {
                                new ScanCondition(
                                    rangeKeyName,
                                    ScanOperator.In,
                                    rangeKeys
                                )
                            }
                    }
                }
            );

            try
            {
                var entity = await search.GetRemainingAsync();
                return entity;
            }
            catch (Exception ex)
            {
                return new List<T>();
            }
        }

        protected async Task<T> GetDBEntry<T>(string hashKey, string rangeKey, string indexName, string propertyName) where T : DynamoDBModel
        {
            var config = new DynamoDBOperationConfig();
            var search = dBContext.QueryAsync<T>(hashKey,
                new DynamoDBOperationConfig()
                {
                    IndexName = indexName,
                    QueryFilter = new List<ScanCondition>() {
                            {
                                new ScanCondition(
                                    propertyName,
                                    ScanOperator.Equal,
                                    [rangeKey]
                                )
                            }
                    }
                }
            );

            try
            {
                var entity = await search.GetRemainingAsync();
                if (entity.Count != 1) return null;
                return entity[0];
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        protected async Task<T> GetDBEntry<T>(string hashKey, string rangeKey) where T : DynamoDBModel
        {

            try
            {
                var entity = await dBContext.LoadAsync<T>(hashKey, rangeKey);
                return entity;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        protected async Task<T> GetDBEntry<T>(string hashKey) where T : DynamoDBModel
        {
            try
            {
                var entity = await dBContext.LoadAsync<T>(hashKey);
                return entity;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        protected string? GetClaimValue(string claimName)
        {
            return HttpContext.User.FindFirstValue(claimName);
        }

        protected List<Claim> GetClaims()
        {
            return HttpContext.User.Claims.ToList();
        }

        protected string GetAccountId()
        {
            string? accountId = GetClaimValue("AccountId");
            if (accountId == null) return string.Empty;
            return accountId;
        }

        protected string GetControllerRoute()
        {
            return RouteData.Values["controller"] as string ?? string.Empty;
        }

        protected long CurrentTimestamp()
        {
            return (long)(DateTime.UtcNow - new DateTime(1970, 1, 1)).TotalSeconds;
        }

        protected async Task<bool> DispatchApiEvent(object payload, MicroserviceType microserviceTarget, string controller, string endpoint, int delayInSeconds = 0)
        {
            return await Task.FromResult(true);
        }



    }
}
