﻿using shared.Models.Documents;
using shared.Models.Documents.DynamoDB;
using shared.Models.Documents.DynamoDB.ProviderData;
using shared.Models.Enums;
using shared.Models.Interfaces;

namespace platform.Models.Request
{
    public class AgentDeployProcessRequest : IStateful<AgentStatus>, IMultiStepProcessWrapper<AwsAgent, AgentStatus>
    {
        public AgentDeployRequest Request { get; set; } = new AgentDeployRequest();
        public AwsAgent Agent { get; set; } = new AwsAgent();
        public AwsAgentTag AgentTag { get; set; } = new AwsAgentTag();
        public AgentStatus Status { get => Agent.Status; set => Agent.Status = value; }

        public AwsAgent GetObject()
        {
            return Agent;
        }
    }
}
