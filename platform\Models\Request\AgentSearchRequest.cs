﻿using System.ComponentModel.DataAnnotations;

namespace platform.Models.Request
{
    public class AgentSearchRequest
    {
        [Required]
        [MinLength(3, ErrorMessage = "Can only search entries using at least 3 characters")]
        public string Query { get; set; } = string.Empty;

        [Range(1, 100, ErrorMessage = "Limit should be a value between 1 and 100")]
        public int Limit { get; set; } = 100;
    }
}
