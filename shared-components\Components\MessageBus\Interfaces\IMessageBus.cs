using shared.Components.MessageBus.Models;
using System.Collections.Generic;

namespace shared.Components.MessageBus.Interfaces
{
    /// <summary>
    /// Interface for message bus service that replaces ApiEventBus with improved functionality
    /// </summary>
    /// 
    public enum MessageBusResult { Success, TooManyRetries, Error }

    public interface IMessageBus : IHostedService
    {
        /// <summary>
        /// Send a message to a target microservice
        /// </summary>
        /// <typeparam name="T">Type of the payload</typeparam>
        /// <param name="request">The message send request containing all parameters</param>
        /// <returns>True if message was queued successfully</returns>
        Task<MessageBusResult> SendAsync<T>(MessageBusSendRequest<T> request);

        Task<MessageBusResult> Requeue(MessageBusMessage message, string? errorMessage = null);

        /// <summary>
        /// Confirm successful processing of a message and delete it from the queue
        /// </summary>
        /// <param name="message">The message to confirm</param>
        /// <returns>Result of the confirmation operation</returns>
        Task<MessageBusResult> ConfirmMessage(MessageBusMessage message);
    }
}
