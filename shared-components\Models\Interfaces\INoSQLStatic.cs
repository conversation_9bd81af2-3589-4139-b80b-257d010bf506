﻿namespace shared.Models.Interfaces
{
    public interface INoSQLStatic
    {
        /// <summary>
        /// Gets the hash key property name for the model.
        /// Override in derived classes to provide specific implementation.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public static abstract string GetHashKeyPropertyName(string? indexName = null);

        /// <summary>
        /// Gets the range key property name for the model.
        /// Override in derived classes to provide specific implementation.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static abstract string? GetRangeKeyPropertyName(string? indexName = null);

        /// <summary>
        /// Gets the table name for the model.
        /// Override in derived classes to provide specific implementation.
        /// </summary>
        /// <returns>Table name</returns>
        public static abstract string GetTableName();
    }
}
