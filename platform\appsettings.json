{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "MicroserviceConfiguration": {"Environment": "development", "InstanceId": "00000000", "InstanceName": "dev01", "Type": "Platform", "Group": "default"}, "UserPoolConfiguration": {"ClientId": "3c3hkorrgc9fno00gf8sieppg1", "UserPoolId": "us-east-1_EXhsl7BgM", "ClientSecret": {"SecretName": "userpool_secret"}, "LoginExpireTimeout": 144000}, "BedrockConfiguration": {"KnowledgeBaseRoleName": "AmazonBedrockExecutionRoleForKnowledgeBase_test_kb1", "AgentRoleName": "AmazonBedrockExecutionRoleForAgents_M62SGE9WGPH", "KnowledgeBaseBucketArn": "arn:aws:s3:::coral-agents-app-dev", "VectorStoreSecretArn": "arn:aws:secretsmanager:us-east-1:911319224041:secret:dev/ricca2/pinecone2-756pno", "VectorStorePath": "https://test-vectorstore-6s5eofv.svc.aped-4627-b74a.pinecone.io", "DefaultEmbeddingsModelArn": "arn:aws:bedrock:us-east-1::foundation-model/amazon.titan-embed-text-v2:0", "PineconeConfiguration": {"Metadata": "metadata", "Text": "data"}}}