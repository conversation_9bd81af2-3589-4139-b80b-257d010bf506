using shared.Components.MultiStepProcess.Enums;
using shared.Components.MultiStepProcess.Models;
using shared.Converters;
using shared.Models.Interfaces;
using System.Text.Json.Serialization;

namespace shared.Components.MultiStepProcess.Interfaces
{
    /// <summary>
    /// Interface for a multi-step process that can execute forward and rollback operations.
    /// Provides a double-linked list builder and runner for executing multiple steps in order
    /// with the ability to revert execution if the process needs to be aborted.
    /// </summary>
    /// <typeparam name="TState">The type of state used by the IStateful object</typeparam>
    /// <typeparam name="TObject">The type of the payload object that implements IStateful</typeparam>
    public interface IMultiStepProcess<TState, TObject, TResult>
        where TObject : IStateful<TState>
        where TState : struct
    {
        /// <summary>
        /// Gets the current state data of the multi-step process.
        /// </summary>
        MultiStepProcessStateData<TState, TObject> StateData { get; }

        /// <summary>
        /// Gets the current state of the IStateful object.
        /// </summary>
        TState CurrentState { get; }

        /// <summary>
        /// Gets the current process state (Running or RollingBack).
        /// </summary>
        MultiStepProcessState ProcessState { get; }

        /// <summary>
        /// Gets whether the process has finished processing.
        /// </summary>
        bool IsFinished { get; }

        /// <summary>
        /// Gets the current retry count for the current step.
        /// </summary>
        int RetryCount { get; }

        /// <summary>
        /// Gets the reason for process failure, if any.
        /// </summary>
        MultiStepProcessFailureReason FailureReason { get; }

        /// <summary>
        /// Gets the process configuration.
        /// </summary>
        MultiStepProcessConfiguration Configuration { get; }

        /// <summary>
        /// Gets whether the last iteration failed.
        /// </summary>
        bool LastIterationFailed { get; }

        /// <summary>
        /// Gets the last exception that occurred, if any.
        /// </summary>
        Exception? LastException { get; }



        /// <summary>
        /// Defines a state transition in the process with ForwardTask and RollbackTask details.
        /// </summary>
        /// <typeparam name="TResult">The type of result returned by the tasks</typeparam>
        /// <param name="fromState">The source state from which this transition starts</param>
        /// <param name="toState">The target state to which this transition leads</param>
        /// <param name="forwardDetails">Details for the forward transition task (task, timeout, max retries)</param>
        /// <param name="rollbackDetails">Details for the rollback transition task (task, timeout, max retries)</param>
        /// <param name="description">Optional description of what this transition does</param>
        /// <returns>This process instance for method chaining</returns>
        IMultiStepProcess<TState, TObject, TResult> DefineTransition(
            TState fromState,
            TState toState,
            MultiStepProcessTransitionDetails<TObject, TResult> forwardDetails,
            MultiStepProcessTransitionDetails<TObject, TResult> rollbackDetails,
            string? description = null);


        /// <summary>
        /// Initializes the process with a TObject instance.
        /// </summary>
        /// <param name="statefulObject">The IStateful object to process</param>
        /// <param name="correlationId">Optional correlation ID for tracking</param>
        void Initialize(TObject statefulObject, string? correlationId = null);

        /// <summary>
        /// Loads the complete state data into the process.
        /// This allows resuming from a previously saved state.
        /// </summary>
        /// <param name="stateData">The state data to load</param>
        void LoadStateData(MultiStepProcessStateData<TState, TObject> stateData);

        /// <summary>
        /// Sets the current state of the process and the IStateful object.
        /// This allows loading state from external configuration or resuming from a saved state.
        /// </summary>
        /// <param name="state">The state to set</param>
        /// <param name="processState">The process state to set</param>
        void SetCurrentState(TState state, MultiStepProcessState processState = MultiStepProcessState.Running);

        /// <summary>
        /// Executes one complete iteration of the process.
        /// Returns a MultiStepProcessResult indicating success and any result object from the task.
        /// </summary>
        /// <returns>MultiStepProcessResult containing success status and result object</returns>
        Task<MultiStepProcessResult<TResult>> ExecuteIteration();

        /// <summary>
        /// Executes the process until it reaches a finished state or fails with a strongly-typed result.
        /// </summary>
        /// <typeparam name="TResult">The expected type of the result object</typeparam>
        /// <param name="maxIterations">Maximum number of iterations to prevent infinite loops</param>
        /// <returns>MultiStepProcessResult containing success status and strongly-typed last result object</returns>
        Task<MultiStepProcessResult<TResult>> ExecuteToCompletion(int maxIterations = 100);

        /// <summary>
        /// Validates that the process configuration is valid and complete.
        /// </summary>
        /// <returns>True if the process is valid</returns>
        /// <exception cref="InvalidOperationException">Thrown when process configuration is invalid</exception>
        bool ValidateProcess();

        /// <summary>
        /// Forces the process to start rolling back from the current state.
        /// </summary>
        void StartRollback();

        /// <summary>
        /// Gets detailed information about the current process state for debugging.
        /// </summary>
        /// <returns>Dictionary containing process state information</returns>
        Dictionary<string, object> GetProcessInfo();

        /// <summary>
        /// Checks if the process can execute another iteration.
        /// </summary>
        /// <returns>True if the process can continue</returns>
        bool CanExecute();
    }
}
