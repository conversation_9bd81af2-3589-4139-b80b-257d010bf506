using shared.Components.MultiStepProcess.Enums;

namespace shared.Components.MultiStepProcess.Models
{
    /// <summary>
    /// Represents the details for a specific direction of a transition (forward or rollback).
    /// Contains the task to execute, timeout configuration, and retry settings.
    /// </summary>
    /// <typeparam name="TObject">The type of the stateful object</typeparam>
    /// <typeparam name="TResult">The type of result returned by the task</typeparam>
    public class MultiStepProcessTransitionDetails<TObject, TResult>
    {
        /// <summary>
        /// The task to execute for this transition direction.
        /// </summary>
        public Func<TObject, Task<(MultiStepProcessTaskRunResult, TResult?)>> Task { get; }

        /// <summary>
        /// Optional timeout for this specific task. If null, uses the process default timeout.
        /// </summary>
        public TimeSpan? Timeout { get; }

        /// <summary>
        /// Optional maximum number of retries for this specific task. If null, uses the process default max retries.
        /// </summary>
        public int? MaxRetries { get; }

        /// <summary>
        /// Initializes a new instance of MultiStepProcessTransitionDetails.
        /// </summary>
        /// <param name="task">The task to execute</param>
        /// <param name="timeout">Optional timeout for the task</param>
        /// <param name="maxRetries">Optional maximum number of retries for the task</param>
        public MultiStepProcessTransitionDetails(
            Func<TObject, Task<(MultiStepProcessTaskRunResult, TResult?)>> task,
            TimeSpan? timeout = null,
            int? maxRetries = null)
        {
            Task = task ?? throw new ArgumentNullException(nameof(task));
            Timeout = timeout;
            MaxRetries = maxRetries;
        }

        /// <summary>
        /// Creates a new instance with the specified timeout.
        /// </summary>
        /// <param name="timeout">The timeout to set</param>
        /// <returns>A new instance with the specified timeout</returns>
        public MultiStepProcessTransitionDetails<TObject, TResult> WithTimeout(TimeSpan timeout)
        {
            return new MultiStepProcessTransitionDetails<TObject, TResult>(Task, timeout, MaxRetries);
        }

        /// <summary>
        /// Creates a new instance with the specified max retries.
        /// </summary>
        /// <param name="maxRetries">The maximum number of retries to set</param>
        /// <returns>A new instance with the specified max retries</returns>
        public MultiStepProcessTransitionDetails<TObject, TResult> WithMaxRetries(int maxRetries)
        {
            return new MultiStepProcessTransitionDetails<TObject, TResult>(Task, Timeout, maxRetries);
        }

        /// <summary>
        /// Executes the task with the stateful object.
        /// </summary>
        /// <param name="statefulObject">The stateful object to pass to the task</param>
        /// <returns>The result of the task execution</returns>
        public async Task<(MultiStepProcessTaskRunResult, TResult?)> ExecuteAsync(TObject statefulObject)
        {
            return await Task(statefulObject);
        }

        /// <summary>
        /// Gets the effective timeout for this task, using the provided default if no specific timeout is set.
        /// </summary>
        /// <param name="defaultTimeout">The default timeout to use if no specific timeout is set</param>
        /// <returns>The effective timeout, or null if neither this task nor the default has a timeout</returns>
        public TimeSpan? GetEffectiveTimeout(TimeSpan? defaultTimeout)
        {
            return Timeout ?? defaultTimeout;
        }

        /// <summary>
        /// Gets the effective max retries for this task, using the provided default if no specific max retries is set.
        /// </summary>
        /// <param name="defaultMaxRetries">The default max retries to use if no specific max retries is set</param>
        /// <returns>The effective max retries</returns>
        public int GetEffectiveMaxRetries(int defaultMaxRetries)
        {
            return MaxRetries ?? defaultMaxRetries;
        }
    }
}
