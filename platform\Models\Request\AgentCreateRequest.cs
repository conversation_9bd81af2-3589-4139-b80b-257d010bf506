using shared.Models.Enums;
using shared.Converters;
using System.Text.Json.Serialization;
using System.ComponentModel.DataAnnotations;

namespace platform.Models.Request
{
    public class AgentCreateRequest
    {
        [Required]
        [MinLength(5)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MinLength(10)]
        public string Description { get; set; } = string.Empty;

        [Required]
        [MinLength(40)]
        public string Instructions { get; set; } = string.Empty;

        [Required]
        [JsonConverter(typeof(JsonEnumStringConverter<AgentType>))]
        public AgentType LLMModelType { get; set; } = AgentType.Claude_v2;
    }
}
