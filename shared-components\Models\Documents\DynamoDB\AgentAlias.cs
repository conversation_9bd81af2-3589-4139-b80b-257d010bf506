﻿using Amazon.DynamoDBv2.DataModel;
using shared.Models.Enums;
using shared.Models.Interfaces;
using shared.Converters;
using System.Text.Json.Serialization;

namespace shared.Models.Documents.DynamoDB
{
    [DynamoDBTable(nameof(AgentAlias))]
    public class AgentAlias : DynamoDBModel, IStateful<AgentStatus>, INoSQLStatic
    {
        public const string AgentIdAliasIndex = "AgentId-Alias-index";

        [DynamoDBGlobalSecondaryIndexHashKey(AgentIdAliasIndex)]
        public string AgentId { get; set; } = string.Empty;
        [DynamoDBRangeKey]
        [DynamoDBGlobalSecondaryIndexRangeKey(AgentIdAliasIndex)]
        public string Alias { get; set; } = string.Empty;

        [DynamoDBHashKey]
        public string AccountId {  get; set; } = string.Empty;
        public string AgentTag { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;

        [DynamoDBProperty(typeof(DynamoEnumStringConverter<AgentStatus>))]
        [JsonConverter(typeof(JsonEnumStringConverter<AgentStatus>))]
        public AgentStatus Status { get; set; } = AgentStatus.QUEUED;

        /// <summary>
        /// Gets the hash key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public static new string GetHashKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table hash key
                return nameof(AgentAlias.AccountId);
            }else if(indexName == AgentIdAliasIndex)
            {
                return nameof(AgentAlias.AgentId);
            }
            return nameof(AgentAlias.AccountId);
        }

        /// <summary>
        /// Gets the range key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            return nameof(AgentAlias.Alias);
        }

        /// <summary>
        /// Gets the table name from DynamoDBTable attribute.
        /// </summary>
        /// <returns>Table name</returns>
        public static new string GetTableName()
        {
            return nameof(AgentAlias);
        }
    }
}
