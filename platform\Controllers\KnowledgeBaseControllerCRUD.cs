﻿using Amazon;
//using Amazon.BedrockAgent;
//using Amazon.BedrockAgent.Model;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using platform.Constants;
using platform.Models.Request;
using platform.Models.Request.KnowledgeBase;
using platform.Models.Response;
using shared.Components.MessageBus.Models;
using shared.Components.MultiStepProcess;
using shared.Components.MultiStepProcess.Enums;
using shared.Components.MultiStepProcess.Interfaces;
using shared.Components.MultiStepProcess.Models;
using shared.Models.Documents.DynamoDB;
using shared.Models.Documents.DynamoDB.ProviderData;
using shared.Models.Enums;
using shared.Models.Response;
using System.Diagnostics.Eventing.Reader;
using System.Text.Json;


namespace platform.Controllers
{

    public partial class KnowledgeBaseController
    {

        #region CREATION PROCESS
        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> CreateProcessCreateDBEntry(AwsKnowledgeBase knowledgeBase)
        {
            if (await PutDBEntry2(knowledgeBase))
            {
                return await Task.FromResult((MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult() { ShouldUpdateDb = false }));
            }
            else
            {
                return await Task.FromResult((MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 10 }));
            }
            
        }
        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> CreateProcessRollbackCreateDBEntry(AwsKnowledgeBase knowledgeBase)
        {
            return (await DeleteDBEntry(knowledgeBase)) ? (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult()) : (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { ShouldUpdateDb = false });
        }
        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> CreateKnowledgeBaseTask(AwsKnowledgeBase knowledgeBase)
        {
            var createKnowledgeBaseRequest = new Amazon.BedrockAgent.Model.CreateKnowledgeBaseRequest();
            createKnowledgeBaseRequest.ClientToken = shared.Helpers.AWSHelper.GenerateToken(knowledgeBase);
            createKnowledgeBaseRequest.Description = knowledgeBase.Description;
            createKnowledgeBaseRequest.Name = knowledgeBase.KbId;

            if (createKnowledgeBaseRequest.Description.Length == 0) createKnowledgeBaseRequest.Description = "no description";

            createKnowledgeBaseRequest.RoleArn = $"arn:aws:iam::{await iIAM.GetProviderAccountNumber()}:role/service-role/{kbConfiguration.CurrentValue.KnowledgeBaseRoleName}";

            createKnowledgeBaseRequest.KnowledgeBaseConfiguration = new Amazon.BedrockAgent.Model.KnowledgeBaseConfiguration();
            createKnowledgeBaseRequest.KnowledgeBaseConfiguration.Type = Amazon.BedrockAgent.KnowledgeBaseType.VECTOR;
            createKnowledgeBaseRequest.KnowledgeBaseConfiguration.VectorKnowledgeBaseConfiguration = new Amazon.BedrockAgent.Model.VectorKnowledgeBaseConfiguration();
            createKnowledgeBaseRequest.KnowledgeBaseConfiguration.VectorKnowledgeBaseConfiguration.EmbeddingModelArn = kbConfiguration.CurrentValue.DefaultEmbeddingsModelArn;
            createKnowledgeBaseRequest.KnowledgeBaseConfiguration.VectorKnowledgeBaseConfiguration.EmbeddingModelConfiguration = new Amazon.BedrockAgent.Model.EmbeddingModelConfiguration();
            createKnowledgeBaseRequest.KnowledgeBaseConfiguration.VectorKnowledgeBaseConfiguration.EmbeddingModelConfiguration.BedrockEmbeddingModelConfiguration = new Amazon.BedrockAgent.Model.BedrockEmbeddingModelConfiguration();
            createKnowledgeBaseRequest.KnowledgeBaseConfiguration.VectorKnowledgeBaseConfiguration.EmbeddingModelConfiguration.BedrockEmbeddingModelConfiguration.Dimensions = 1024;

            createKnowledgeBaseRequest.StorageConfiguration = new Amazon.BedrockAgent.Model.StorageConfiguration();
            createKnowledgeBaseRequest.StorageConfiguration.Type = Amazon.BedrockAgent.KnowledgeBaseStorageType.PINECONE;
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration = new Amazon.BedrockAgent.Model.PineconeConfiguration();
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration.Namespace = knowledgeBase.KbId;
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration.ConnectionString = kbConfiguration.CurrentValue.VectorStorePath;
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration.CredentialsSecretArn = kbConfiguration.CurrentValue.VectorStoreSecretArn;
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration.FieldMapping = new Amazon.BedrockAgent.Model.PineconeFieldMapping();
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration.FieldMapping.MetadataField = kbConfiguration.CurrentValue.PineconeConfiguration.Metadata;
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration.FieldMapping.TextField = kbConfiguration.CurrentValue.PineconeConfiguration.Text;

            try
            {
                var resp = await bedrockAgent.CreateKnowledgeBaseAsync(createKnowledgeBaseRequest);
                knowledgeBase.AwsData.AwsKbId = resp.KnowledgeBase.KnowledgeBaseId;

                return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
            }
            catch (Exception ex)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult());
            }
        }
        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> DeleteKnowledgeBaseTask(AwsKnowledgeBase knowledgeBase)
        {
            try
            {
                var resp = await bedrockAgent.DeleteKnowledgeBaseAsync(new Amazon.BedrockAgent.Model.DeleteKnowledgeBaseRequest() { KnowledgeBaseId=knowledgeBase.AwsData.AwsKbId });
                knowledgeBase.AwsData.AwsKbId = null;

                return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
            }
            catch (Exception ex)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult());
            }
        }
        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> CreateProcessCreateDatasource(AwsKnowledgeBase knowledgeBase)
        {
            var kbRequest = new Amazon.BedrockAgent.Model.GetKnowledgeBaseRequest() { KnowledgeBaseId= knowledgeBase.AwsData.AwsKbId};
            var kbRequestResponse = await bedrockAgent.GetKnowledgeBaseAsync(kbRequest);

            if (kbRequestResponse == null || kbRequestResponse.KnowledgeBase.Status != Amazon.BedrockAgent.KnowledgeBaseStatus.ACTIVE)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 20 });
            }


            var awsRequest = new Amazon.BedrockAgent.Model.CreateDataSourceRequest();

            awsRequest.Name = knowledgeBase.KbId + "-ds";
            awsRequest.Description = knowledgeBase.Description;
            if (knowledgeBase.AwsData == null) throw new Exception("knjowledgebase provider data was null");
            awsRequest.KnowledgeBaseId = knowledgeBase.AwsData.AwsKbId;

            awsRequest.VectorIngestionConfiguration = new Amazon.BedrockAgent.Model.VectorIngestionConfiguration();

            awsRequest.VectorIngestionConfiguration.ChunkingConfiguration = new Amazon.BedrockAgent.Model.ChunkingConfiguration();
            awsRequest.VectorIngestionConfiguration.ChunkingConfiguration.ChunkingStrategy = Amazon.BedrockAgent.ChunkingStrategy.FIXED_SIZE;
            awsRequest.VectorIngestionConfiguration.ChunkingConfiguration.FixedSizeChunkingConfiguration = new Amazon.BedrockAgent.Model.FixedSizeChunkingConfiguration();
            awsRequest.VectorIngestionConfiguration.ChunkingConfiguration.FixedSizeChunkingConfiguration.MaxTokens = 300;
            awsRequest.VectorIngestionConfiguration.ChunkingConfiguration.FixedSizeChunkingConfiguration.OverlapPercentage = 20;

            awsRequest.DataSourceConfiguration = new Amazon.BedrockAgent.Model.DataSourceConfiguration();
            awsRequest.DataSourceConfiguration.Type = Amazon.BedrockAgent.DataSourceType.S3;

            awsRequest.DataSourceConfiguration.S3Configuration = new Amazon.BedrockAgent.Model.S3DataSourceConfiguration();
            awsRequest.DataSourceConfiguration.S3Configuration.BucketArn = kbConfiguration.CurrentValue.KnowledgeBaseBucketArn;
            awsRequest.DataSourceConfiguration.S3Configuration.InclusionPrefixes = new List<string> { $"kbs/AccountId={knowledgeBase.AccountId}/KbId={knowledgeBase.KbId}/" };
            awsRequest.ClientToken = shared.Helpers.AWSHelper.GenerateToken(knowledgeBase);

            try
            {
                var resp = await bedrockAgent.CreateDataSourceAsync(awsRequest);

                knowledgeBase.AwsData.ExternalDataSourcesIds.Add(resp.DataSource.DataSourceId);
                knowledgeBase.Status = shared.Models.Enums.KnowledgeBaseStatus.DATASOURCE_CREATED;

            }
            catch (Exception ex)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 30 });
            }


            return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> CreateProcessDeleteDatasource(AwsKnowledgeBase knowledgeBase)
        {
            var kbRequest = new Amazon.BedrockAgent.Model.GetKnowledgeBaseRequest() { KnowledgeBaseId = knowledgeBase.AwsData.AwsKbId };
            var kbRequestResponse = await bedrockAgent.GetKnowledgeBaseAsync(kbRequest);

            if (kbRequestResponse == null || kbRequestResponse.KnowledgeBase.Status != Amazon.BedrockAgent.KnowledgeBaseStatus.ACTIVE)
            {
                return (MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 20 });
            }


            foreach(string ds in knowledgeBase.AwsData.ExternalDataSourcesIds)
            {
                var awsRequest = new Amazon.BedrockAgent.Model.DeleteDataSourceRequest();
                awsRequest.KnowledgeBaseId = knowledgeBase.AwsData.AwsKbId;
                awsRequest.DataSourceId = ds;

                var resp = await bedrockAgent.DeleteDataSourceAsync(awsRequest);
            }
          
   
            return (MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult());
        }

        private IMultiStepProcess<KnowledgeBaseStatus, AwsKnowledgeBase, SuperControllerMultiStepResult> GetCreationProcess(){

            var process = new MultiStepProcess<KnowledgeBaseStatus, AwsKnowledgeBase, SuperControllerMultiStepResult>()
                .DefineTransition(
                    KnowledgeBaseStatus.QUEUED,
                    KnowledgeBaseStatus.DB_ENTRY_CREATED,
                    new MultiStepProcessTransitionDetails<AwsKnowledgeBase, SuperControllerMultiStepResult>(CreateProcessCreateDBEntry, maxRetries: 10),
                    new MultiStepProcessTransitionDetails<AwsKnowledgeBase, SuperControllerMultiStepResult>(CreateProcessRollbackCreateDBEntry, maxRetries: 10),
                    "Create DB Entry"
                )
                .DefineTransition(
                    KnowledgeBaseStatus.DB_ENTRY_CREATED,
                    KnowledgeBaseStatus.KB_CREATED,
                    new MultiStepProcessTransitionDetails<AwsKnowledgeBase, SuperControllerMultiStepResult>(CreateKnowledgeBaseTask, maxRetries: 10),
                    new MultiStepProcessTransitionDetails<AwsKnowledgeBase, SuperControllerMultiStepResult>(DeleteKnowledgeBaseTask, maxRetries: 10),
                    "Create KnowledgeBase"
                )
                .DefineTransition(
                    KnowledgeBaseStatus.KB_CREATED,
                    KnowledgeBaseStatus.READY,
                    new MultiStepProcessTransitionDetails<AwsKnowledgeBase, SuperControllerMultiStepResult>(CreateProcessCreateDatasource, maxRetries: 20),
                    new MultiStepProcessTransitionDetails<AwsKnowledgeBase, SuperControllerMultiStepResult>(CreateProcessDeleteDatasource, maxRetries: 20),
                    "Create Datasource and finish"
                );

            return process;
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost]
        public async Task<IActionResult> CreateRequest([FromBody] Models.Request.KnowledgeBaseCreateRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            string accountId = GetAccountId();
            string kbId = await uniqueIDService.GenerateUniqueIdAsync(ModelType.KnowledgeBase);

            AwsKnowledgeBase knowledgeBase = new AwsKnowledgeBase();
            knowledgeBase.Name = request.Name;
            knowledgeBase.Description = request.Description;
            knowledgeBase.AccountId = accountId;
            knowledgeBase.KbId = kbId;
            knowledgeBase.Status = KnowledgeBaseStatus.QUEUED;
            knowledgeBase.LastChangeTimestamp = CurrentTimestamp();
            knowledgeBase.AwsData = new AwsKnowledgeBaseData();

            var process = GetCreationProcess();
            process.Initialize(knowledgeBase);

            var messageBusParams = new MessageBusDispachParams()
            {
                Controller = Routes.KnowledgeBaseController.BasePath,
                Route = Routes.KnowledgeBaseController.Internal.CREATION_PROCESS,
                TargetMicroservice = MicroserviceType.Platform,
                Payload = process.StateData
            };

            await DispatchApiEventV2(messageBusParams);

            return Ok(request);
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.MicroserviceAuthScheme)]
        [Route(Constants.Routes.KnowledgeBaseController.Internal.CREATION_PROCESS)]
        [HttpPost]
        public async Task<IActionResult> CreateProcess([FromBody] MessageBusMessage message)
        {
            return await MultiStepProcess(message, GetCreationProcess());
        }
        #endregion


        #region LIST REQUEST
        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet]
        public async Task<IActionResult> ListKnowledgeBases([FromQuery] KnowledgeBaseListRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }


            var listEntriesInternal = await GetDBEntries2<AwsKnowledgeBase>(GetAccountId(), request.Count, request.Next);

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<AwsKnowledgeBase, KnowledgeBaseResponse>();
            }, loggerFactory).CreateMapper();


            var result = new ListResponse<KnowledgeBaseResponse>()
            {
                Entries = mapper.Map<IList<AwsKnowledgeBase>, IList<KnowledgeBaseResponse>>(listEntriesInternal.Entries).ToList(),
                NextToken = listEntriesInternal.NextToken,
                Total = listEntriesInternal.Total,
            };

            return Ok(result);
        }
        #endregion


        #region UPDATE REQUEST
        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPut(Constants.Routes.KnowledgeBaseController.Public.PUT)]
        public async Task<IActionResult> PutKnowledgeBase(string kbId, [FromBody] KnowledgeBasePutRequest knowledgeBasePutRequest)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            string accountId = GetAccountId();
            KnowledgeBase kb = await GetDBEntry<KnowledgeBase>(accountId, kbId);
            if (kb == null) return BadRequest("Not found");

            kb.Name = knowledgeBasePutRequest.Name;
            kb.Description = knowledgeBasePutRequest.Description;

            if (await UpdateDBEntry2(kb, new List<string>() { nameof(KnowledgeBase.Name), nameof(KnowledgeBase.Description) }))
            {
                return StatusCode(500, "Couldn't update");
            }
            return Ok(kb);
        }
        #endregion


        #region GET REQUEST
        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.KnowledgeBaseController.Public.GET_KNOWLEDGEBASE)]
        public async Task<IActionResult> GetKnowledgeBase([FromRoute] KnowledgeBaseGetRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            string accountId = GetAccountId();
            AwsKnowledgeBase kb = await GetDBEntry<AwsKnowledgeBase>(accountId, request.KbId);
            if (kb == null) return BadRequest("Not found");

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<AwsKnowledgeBase, KnowledgeBaseResponse>();
            }, loggerFactory).CreateMapper();
            
            return Ok(mapper.Map<AwsKnowledgeBase, KnowledgeBaseResponse>(kb));
        }
        #endregion


        #region LIST AGENTS FOR KNOWLEDGEBASE
        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.KnowledgeBaseController.Public.GET_AGENTS_FOR_KNOWLEDGE_BASE)]
        public async Task<IActionResult> GetAgentsForKnowledgeBase([FromRoute] string kbId, [FromQuery] KnowledgeBaseListAgentsRequest request)
        {
            if (!ModelState.IsValid) {  return BadRequest(ModelState); }


            var joinConfig = new DBJoinConfig<AgentKnowledgeBase, Agent>
            {
                FirstTable = new TableQueryConfig
                {
                    HashKeyValue = GetAccountId(),
                    AdditionalFilterExpression = new Amazon.DynamoDBv2.DocumentModel.Expression()
                    {
                        ExpressionAttributeNames = new Dictionary<string, string>
                            {
                                { $"#{nameof(AgentKnowledgeBase.KnowledgebaseId)}", nameof(AgentKnowledgeBase.KnowledgebaseId) }
                            },
                        ExpressionAttributeValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry>
                            {
                                { $":{nameof(AgentKnowledgeBase.KnowledgebaseId)}", kbId }
                            },
                        ExpressionStatement = $"#{nameof(AgentKnowledgeBase.KnowledgebaseId)}=:{nameof(AgentKnowledgeBase.KnowledgebaseId)}"
                    }
                },
                SecondTable = new TableQueryConfig
                {
                    HashKeyValue = GetAccountId(),
                    Index = null
                },
                JoinCondition = new JoinCondition
                {
                    FirstTableField = nameof(AgentKnowledgeBase.AgentId),
                    SecondTableField = nameof(Agent.AgentId)
                },
                TargetResultCount = request.Count
            };


            // Perform the join
            var joinResult = await GetDBJoin(joinConfig, request.Next);

            // Extract just the KnowledgeBase entries for the response
            var agents = joinResult.Entries.Select(entry => entry.Second).ToList();

            var response = new ListResponse<Agent>
            {
                Entries = agents,
                NextToken = joinResult.NextToken,
                Total = joinResult.Total
            };

            return Ok(response);

        }
        #endregion

    }
}
        