using Amazon.DynamoDBv2.DataModel;
using shared.Models.Enums;
using shared.Converters;
using System.Text.Json.Serialization;
using shared.Models.Interfaces;

namespace shared.Models.Documents.DynamoDB
{

    [DynamoDBTable(nameof(KnowledgeBase))]
    public class KnowledgeBase : DynamoDBModel, IStateful<KnowledgeBaseStatus>
    {

        [DynamoDBHashKey]
        public string AccountId { get; set; } = string.Empty;

        [DynamoDBRangeKey]
        public string KbId { get; set; } = string.Empty;

        [DynamoDBProperty(typeof(DynamoEnumStringConverter<KnowledgeBaseStatus>))]
        [JsonConverter(typeof(JsonEnumStringConverter<KnowledgeBaseStatus>))]
        public KnowledgeBaseStatus Status { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Tag {  get; set; } = string.Empty;

        public override string GetSearchString()
        {
            return Name.ToLower();
        }

        /// <summary>
        /// Gets the hash key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public static new string? GetHashKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table hash key
                return nameof(KnowledgeBase.AccountId);
            }
            return null;
        }

        /// <summary>
        /// Gets the range key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            if (string.IsNullOrEmpty(indexName))
            {
                // Primary table range key
                return nameof(KnowledgeBase.KbId);
            }
            return null;
        }

        /// <summary>
        /// Gets the table name from DynamoDBTable attribute.
        /// </summary>
        /// <returns>Table name</returns>
        public static new string GetTableName()
        {
            return nameof(KnowledgeBase);
        }

        /// <summary>
        /// Gets the hash key value from the model instance.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key value</returns>
        public override string? GetHashKeyValue(string? indexName = null)
        {
            return AccountId;
        }

        /// <summary>
        /// Gets the range key value from the model instance.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key value</returns>
        public override string? GetRangeKeyValue(string? indexName = null)
        {
            return KbId;
        }
    }
}
