using shared.Components.MultiStepProcess.Enums;
using shared.Components.MultiStepProcess.Exceptions;
using shared.Components.MultiStepProcess.Interfaces;
using shared.Components.MultiStepProcess.Models;
using shared.Models.Interfaces;
using System.Collections.Concurrent;

namespace shared.Components.MultiStepProcess
{
    /// <summary>
    /// Implementation of a multi-step process that can execute forward and rollback operations.
    /// Provides a double-linked list builder and runner for executing multiple steps in order
    /// with the ability to revert execution if the process needs to be aborted.
    /// </summary>
    /// <typeparam name="TState">The type of state used by the IStateful object</typeparam>
    /// <typeparam name="TObject">The type of the payload object that implements IStateful</typeparam>
    public class MultiStepProcess<TState, TObject, TResult> : IMultiStepProcess<TState, TObject, TResult>
        where TObject : IStateful<TState>
        where TState : struct
    {
        private readonly ConcurrentDictionary<(TState FromState, TState ToState), MultiStepProcessStateTransition<TState, TObject, TResult>> _transitions;
        private readonly object _lockObject = new object();

        private MultiStepProcessStateData<TState, TObject>? _stateData;
        private bool _lastIterationFailed;
        private Exception? _lastException;


        /// <summary>
        /// Gets the current state data of the multi-step process.
        /// </summary>
        public MultiStepProcessStateData<TState, TObject> StateData => 
            _stateData ?? throw new InvalidOperationException("Process has not been initialized. Call Initialize() or LoadStateData() first.");

        /// <summary>
        /// Gets the current state of the IStateful object.
        /// </summary>
        public TState CurrentState => StateData.CurrentState;

        /// <summary>
        /// Gets the current process state (Running or RollingBack).
        /// </summary>
        public MultiStepProcessState ProcessState => StateData.ProcessState;

        /// <summary>
        /// Gets whether the process has finished processing.
        /// </summary>
        public bool IsFinished => StateData.IsFinished;

        /// <summary>
        /// Gets the current retry count for the current step.
        /// </summary>
        public int RetryCount => StateData.RetryCount;

        /// <summary>
        /// Gets the reason for process failure, if any.
        /// </summary>
        public MultiStepProcessFailureReason FailureReason => StateData.FailureReason;

        /// <summary>
        /// Gets the process configuration.
        /// </summary>
        public MultiStepProcessConfiguration Configuration { get; private set; }

        /// <summary>
        /// Gets whether the last iteration failed.
        /// </summary>
        public bool LastIterationFailed => _lastIterationFailed;

        /// <summary>
        /// Gets the last exception that occurred, if any.
        /// </summary>
        public Exception? LastException => _lastException;

        /// <summary>
        /// Creates a new MultiStepProcess with default configuration.
        /// </summary>
        public MultiStepProcess() : this(new MultiStepProcessConfiguration())
        {
        }

        /// <summary>
        /// Creates a new MultiStepProcess with the specified configuration.
        /// </summary>
        /// <param name="configuration">Process configuration</param>
        public MultiStepProcess(MultiStepProcessConfiguration configuration)
        {
            Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _transitions = new ConcurrentDictionary<(TState FromState, TState ToState), MultiStepProcessStateTransition<TState, TObject, TResult>>();

            if (Configuration.ValidateConfigurationOnInit)
            {
                Configuration.Validate();
            }
        }

        /// <summary>
        /// Creates a new MultiStepProcess with an initial object and default configuration.
        /// </summary>
        /// <param name="statefulObject">The initial IStateful object</param>
        /// <param name="correlationId">Optional correlation ID</param>
        public MultiStepProcess(TObject statefulObject, string? correlationId = null) 
            : this(new MultiStepProcessConfiguration())
        {
            Initialize(statefulObject, correlationId);
        }

        /// <summary>
        /// Creates a new MultiStepProcess with an initial object and specified configuration.
        /// </summary>
        /// <param name="statefulObject">The initial IStateful object</param>
        /// <param name="configuration">Process configuration</param>
        /// <param name="correlationId">Optional correlation ID</param>
        public MultiStepProcess(TObject statefulObject, MultiStepProcessConfiguration configuration, string? correlationId = null) 
            : this(configuration)
        {
            Initialize(statefulObject, correlationId);
        }





        /// <summary>
        /// Defines a state transition in the process with ForwardTask and RollbackTask details.
        /// </summary>
        /// <typeparam name="TResult">The type of result returned by the tasks</typeparam>
        /// <param name="fromState">The source state from which this transition starts</param>
        /// <param name="toState">The target state to which this transition leads</param>
        /// <param name="forwardDetails">Details for the forward transition task (task, timeout, max retries)</param>
        /// <param name="rollbackDetails">Details for the rollback transition task (task, timeout, max retries)</param>
        /// <param name="description">Optional description of what this transition does</param>
        /// <returns>This process instance for method chaining</returns>
        public IMultiStepProcess<TState, TObject, TResult> DefineTransition(
            TState fromState,
            TState toState,
            MultiStepProcessTransitionDetails<TObject, TResult> forwardDetails,
            MultiStepProcessTransitionDetails<TObject, TResult> rollbackDetails,
            string? description = null)
        {
            if (forwardDetails == null) throw new ArgumentNullException(nameof(forwardDetails));
            if (rollbackDetails == null) throw new ArgumentNullException(nameof(rollbackDetails));

            var transition = new MultiStepProcessStateTransition<TState, TObject, TResult>(
                fromState,
                toState,
                forwardDetails,
                rollbackDetails,
                description);

            var key = (fromState, toState);
            _transitions.AddOrUpdate(key, transition, (k, existing) => transition);

            return this;
        }

















        /// <summary>
        /// Gets the appropriate transition for the current state and process direction.
        /// </summary>
        /// <param name="currentState">The current state</param>
        /// <param name="isForward">True for forward execution, false for rollback</param>
        /// <returns>The transition to execute and the target state</returns>
        private (MultiStepProcessStateTransition<TState, TObject, TResult> transition, TState targetState)? GetTransitionForExecution(TState currentState, bool isForward)
        {
            if (isForward)
            {
                // For forward execution, look for any transition FROM current state
                var forwardTransition = _transitions.FirstOrDefault(kvp => kvp.Key.FromState.Equals(currentState));
                if (!forwardTransition.Equals(default(KeyValuePair<(TState FromState, TState ToState), MultiStepProcessStateTransition<TState, TObject, TResult>>)))
                {
                    return (forwardTransition.Value, forwardTransition.Key.ToState);
                }
            }
            else
            {
                // For rollback execution, look for any transition TO current state
                var rollbackTransition = _transitions.FirstOrDefault(kvp => kvp.Key.ToState.Equals(currentState));
                if (!rollbackTransition.Equals(default(KeyValuePair<(TState FromState, TState ToState), MultiStepProcessStateTransition<TState, TObject, TResult>>)))
                {
                    return (rollbackTransition.Value, rollbackTransition.Key.FromState);
                }
            }

            return null;
        }



        /// <summary>
        /// Initializes the process with a TObject instance.
        /// </summary>
        public void Initialize(TObject statefulObject, string? correlationId = null)
        {
            if (statefulObject == null) throw new ArgumentNullException(nameof(statefulObject));
            if (_stateData != null) throw new InvalidOperationException("Process has already been initialized. Use LoadStateData() to replace existing state.");

            _stateData = new MultiStepProcessStateData<TState, TObject>(statefulObject, correlationId);
            _lastIterationFailed = false;
            _lastException = null;
        }

        /// <summary>
        /// Loads the complete state data into the process.
        /// </summary>
        public void LoadStateData(MultiStepProcessStateData<TState, TObject> stateData)
        {
            _stateData = stateData ?? throw new ArgumentNullException(nameof(stateData));
            _lastIterationFailed = false;
            _lastException = null;
        }

        /// <summary>
        /// Sets the current state of the process and the IStateful object.
        /// </summary>
        public void SetCurrentState(TState state, MultiStepProcessState processState = MultiStepProcessState.Running)
        {
            if (_stateData == null) throw new InvalidOperationException("Process has not been initialized. Call Initialize() or LoadStateData() first.");

            lock (_lockObject)
            {
                _stateData.StatefulObject.Status = state;
                _stateData.UpdateProcessState(processState);
                _stateData.ResetRetryCount();
                _lastIterationFailed = false;
                _lastException = null;
            }
        }

        /// <summary>
        /// Forces the process to start rolling back from the current state.
        /// </summary>
        public void StartRollback()
        {
            if (_stateData == null) throw new InvalidOperationException("Process has not been initialized. Call Initialize() or LoadStateData() first.");

            lock (_lockObject)
            {
                _stateData.UpdateProcessState(MultiStepProcessState.RollingBack);
                _stateData.ResetRetryCount();
            }
        }

        /// <summary>
        /// Validates that the process configuration is valid and complete.
        /// </summary>
        public bool ValidateProcess()
        {
            if (_transitions.IsEmpty)
                throw new ProcessConfigurationException("No transitions have been configured. Use DefineTransition() to add transitions.");

            // Validate configuration
            try
            {
                Configuration.Validate();
            }
            catch (ArgumentException ex)
            {
                throw new ProcessConfigurationException($"Invalid process configuration: {ex.Message}", ex);
            }

            // Validate that all transitions have valid tasks
            foreach (var kvp in _transitions)
            {
                var transitionKey = kvp.Key;
                var transition = kvp.Value;

                if (transition.ForwardDetails?.Task == null)
                {
                    throw new ProcessConfigurationException($"Transition from '{transitionKey.FromState}' to '{transitionKey.ToState}' has no forward task defined.");
                }

                if (transition.RollbackDetails?.Task == null)
                {
                    throw new ProcessConfigurationException($"Transition from '{transitionKey.FromState}' to '{transitionKey.ToState}' has no rollback task defined.");
                }
            }

            return true;
        }





        /// <summary>
        /// Checks if the process can execute another iteration.
        /// </summary>
        public bool CanExecute()
        {
            return _stateData != null && !_stateData.IsFinished;
        }

        /// <summary>
        /// Executes one complete iteration of the process with a strongly-typed result.
        /// </summary>
        /// <typeparam name="TResult">The expected type of the result object</typeparam>
        public async Task<MultiStepProcessResult<TResult>> ExecuteIteration()
        {
            if (_stateData == null) throw new InvalidOperationException("Process has not been initialized. Call Initialize() or LoadStateData() first.");
            if (_stateData.IsFinished) throw new InvalidOperationException("Process has already finished and cannot execute further iterations.");

            try
            {
                _lastIterationFailed = false;
                _lastException = null;

                // Get the appropriate transition for the current state and direction
                bool isForward = _stateData.ProcessState == MultiStepProcessState.Running;
                var transitionInfo = GetTransitionForExecution(_stateData.CurrentState, isForward);

                if (transitionInfo == null)
                {
                    // No transition available - this means we've reached a terminal state
                    _stateData.MarkAsFinished(MultiStepProcessFailureReason.None);
                    return MultiStepProcessResult<TResult>.CreateSuccess(default(TResult));
                }

                var (transition, targetState) = transitionInfo.Value;

                // Execute the appropriate task based on process state
                var (taskResult, resultObject) = await ExecuteTransitionTask(transition, isForward);

                // Handle the task result
                return await HandleTransitionTaskResult(taskResult, resultObject, transition, targetState, isForward);
            }
            catch (Exception ex)
            {
                _lastException = ex;
                _lastIterationFailed = true;
                _stateData.MarkAsFinished(MultiStepProcessFailureReason.TaskAborted);
                return MultiStepProcessResult<TResult>.CreateFailure(default(TResult));
            }
        }

        /// <summary>
        /// Executes the process until it reaches a finished state or fails with a strongly-typed result.
        /// </summary>
        /// <typeparam name="TResult">The expected type of the result object</typeparam>
        public async Task<MultiStepProcessResult<TResult>> ExecuteToCompletion(int maxIterations = 100)
        {
            int iterations = 0;
            TResult? lastResult = default(TResult);

            while (!IsFinished && iterations < maxIterations)
            {
                var iterationResult = await ExecuteIteration();
                lastResult = iterationResult.Result;

                if (!iterationResult.Success)
                {
                    return MultiStepProcessResult<TResult>.CreateFailure(lastResult);
                }

                iterations++;
            }

            return new MultiStepProcessResult<TResult>(IsFinished, lastResult);
        }

        /// <summary>
        /// Executes the appropriate task for the current transition and direction.
        /// </summary>
        private async Task<(MultiStepProcessTaskRunResult, TResult?)> ExecuteTransitionTask(MultiStepProcessStateTransition<TState, TObject, TResult> transition, bool isForward)
        {
            var timeout = transition.GetTimeoutForDirection(isForward, Configuration.DefaultTaskTimeout);

            try
            {
                if (timeout.HasValue)
                {
                    // Execute with timeout
                    using var cts = new CancellationTokenSource(timeout.Value);
                    Task<(MultiStepProcessTaskRunResult, TResult?)> taskWithCancellation;

                    if (isForward)
                    {
                        taskWithCancellation = Task.Run(async () => await transition.ExecuteForwardTaskAsObjectAsync(_stateData.StatefulObject), cts.Token);
                    }
                    else
                    {
                        taskWithCancellation = Task.Run(async () => await transition.ExecuteRollbackTaskAsObjectAsync(_stateData.StatefulObject), cts.Token);
                    }

                    return await taskWithCancellation;
                }
                else
                {
                    // Execute without timeout
                    if (isForward)
                    {
                        return await transition.ExecuteForwardTaskAsObjectAsync(_stateData.StatefulObject);
                    }
                    else
                    {
                        return await transition.ExecuteRollbackTaskAsObjectAsync(_stateData.StatefulObject);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // Task timed out
                return (MultiStepProcessTaskRunResult.FailedTimeout, default(TResult));
            }
            catch (Exception ex)
            {
                // Wrap and rethrow as transition execution exception
                var direction = isForward ? "forward" : "rollback";
                var fromState = isForward ? transition.FromState : transition.ToState;
                var toState = isForward ? transition.ToState : transition.FromState;
                throw new StepTaskExecutionException(
                    _stateData.CurrentState,
                    _stateData.ProcessState,
                    $"Transition task execution failed for {direction} transition from '{fromState}' to '{toState}': {ex.Message}",
                    ex);
            }
        }

        /// <summary>
        /// Handles the result of a transition task execution and determines next actions with strongly-typed result.
        /// </summary>
        private async Task<MultiStepProcessResult<TResult>> HandleTransitionTaskResult(
            MultiStepProcessTaskRunResult taskResult,
            TResult? resultObject,
            MultiStepProcessStateTransition<TState, TObject, TResult> transition,
            TState targetState,
            bool isForward)
        {
            switch (taskResult)
            {
                case MultiStepProcessTaskRunResult.Successful:
                    return HandleSuccessfulTransition(resultObject, transition, targetState, isForward);

                case MultiStepProcessTaskRunResult.FailedRetry:
                    return HandleRetryableTransitionFailure(resultObject, transition, isForward);

                case MultiStepProcessTaskRunResult.FailedAbort:
                    return HandleAbortFailure(resultObject);

                case MultiStepProcessTaskRunResult.FailedTimeout:
                    return HandleTimeoutTransitionFailure(resultObject, transition, isForward);

                default:
                    throw new ArgumentOutOfRangeException(nameof(taskResult), taskResult, "Unknown task result");
            }
        }

        /// <summary>
        /// Handles successful transition execution with strongly-typed result.
        /// </summary>
        private MultiStepProcessResult<TResult> HandleSuccessfulTransition(
            object? resultObject,
            MultiStepProcessStateTransition<TState, TObject, TResult> transition,
            TState targetState,
            bool isForward)
        {
            lock (_lockObject)
            {
                // Reset retry count on successful transition if configured
                if (Configuration.ResetRetryCountOnStateTransition)
                {
                    _stateData.ResetRetryCount();
                }

                // Try to cast the result to the expected type
                TResult? typedResult = default(TResult);
                if (resultObject != null)
                {
                    try
                    {
                        typedResult = (TResult)resultObject;
                    }
                    catch (InvalidCastException)
                    {
                        // If cast fails, use default value for TResult
                        typedResult = default(TResult);
                    }
                }

                // Transition to the target state
                _stateData.StatefulObject.Status = targetState;
                return MultiStepProcessResult<TResult>.CreateSuccess(typedResult);
            }
        }

        /// <summary>
        /// Handles retryable transition failure with strongly-typed result.
        /// </summary>
        private MultiStepProcessResult<TResult> HandleRetryableTransitionFailure(
            TResult? resultObject,
            MultiStepProcessStateTransition<TState, TObject, TResult> transition,
            bool isForward)
        {
            lock (_lockObject)
            {
                // Get the effective max retries for the current task type (ForwardTask or RollbackTask)
                var effectiveMaxRetries = transition.GetEffectiveMaxRetries(isForward, Configuration.MaxRetries);

                if (_stateData.IsRetryLimitExceeded(effectiveMaxRetries))
                {
                    // Retry limit exceeded - handle based on process state
                    return HandleRetryLimitExceeded(resultObject, effectiveMaxRetries);
                }

                // Try to cast the result to the expected type
                TResult? typedResult = default(TResult);
                if (resultObject != null)
                {
                    try
                    {
                        typedResult = (TResult)resultObject;
                    }
                    catch (InvalidCastException)
                    {
                        // If cast fails, use default value for TResult
                        typedResult = default(TResult);
                    }
                }

                // Increment retry count and continue
                _stateData.IncrementRetryCount();
                return MultiStepProcessResult<TResult>.CreateSuccess(typedResult); // Retry is considered a successful iteration
            }
        }

        /// <summary>
        /// Handles abort task failure with strongly-typed result.
        /// </summary>
        private MultiStepProcessResult<TResult> HandleAbortFailure(TResult? resultObject)
        {
            lock (_lockObject)
            {
                // Try to cast the result to the expected type
                TResult? typedResult = default(TResult);
                if (resultObject != null)
                {
                    try
                    {
                        typedResult = (TResult)resultObject;
                    }
                    catch (InvalidCastException)
                    {
                        // If cast fails, use default value for TResult
                        typedResult = default(TResult);
                    }
                }

                if (_stateData.ProcessState == MultiStepProcessState.Running)
                {
                    // Switch to rollback mode
                    _stateData.UpdateProcessState(MultiStepProcessState.RollingBack);
                    _stateData.ResetRetryCount();
                    return MultiStepProcessResult<TResult>.CreateSuccess(typedResult); // Switching to rollback is considered successful
                }
                else
                {
                    // Already in rollback mode - process cannot continue
                    _stateData.MarkAsFinished(MultiStepProcessFailureReason.RollbackFailed);
                    _lastIterationFailed = true;
                    return MultiStepProcessResult<TResult>.CreateFailure(typedResult);
                }
            }
        }

        /// <summary>
        /// Handles timeout transition failure based on configuration with strongly-typed result.
        /// </summary>
        private MultiStepProcessResult<TResult> HandleTimeoutTransitionFailure(
            TResult? resultObject,
            MultiStepProcessStateTransition<TState, TObject, TResult> transition,
            bool isForward)
        {
            // Handle timeout based on configuration
            if (Configuration.TimeoutBehavior == MultiStepProcessTaskRunResult.FailedRetry)
            {
                return HandleRetryableTransitionFailure(resultObject, transition, isForward);
            }
            else
            {
                return HandleAbortFailure(resultObject);
            }
        }

        /// <summary>
        /// Handles retry limit exceeded scenario with strongly-typed result.
        /// </summary>
        private MultiStepProcessResult<TResult> HandleRetryLimitExceeded(TResult? resultObject, int effectiveMaxRetries)
        {
            // Create the retry exceeded exception for tracking
            var retryException = new MaxRetryExceededException(_stateData.CurrentState, _stateData.RetryCount, effectiveMaxRetries);
            _lastException = retryException;

            // Try to cast the result to the expected type
            TResult? typedResult = default(TResult);
            if (resultObject != null)
            {
                try
                {
                    typedResult = (TResult)resultObject;
                }
                catch (InvalidCastException)
                {
                    // If cast fails, use default value for TResult
                    typedResult = default(TResult);
                }
            }

            if (_stateData.ProcessState == MultiStepProcessState.Running)
            {
                // Switch to rollback mode
                _stateData.UpdateProcessState(MultiStepProcessState.RollingBack);
                _stateData.ResetRetryCount();
                return MultiStepProcessResult<TResult>.CreateSuccess(typedResult);
            }
            else
            {
                // Already in rollback mode - process cannot continue
                _stateData.MarkAsFinished(MultiStepProcessFailureReason.RetryLimitExceeded);
                _lastIterationFailed = true;
                return MultiStepProcessResult<TResult>.CreateFailure(typedResult);
            }
        }

        /// <summary>
        /// Gets detailed information about the current process state for debugging.
        /// </summary>
        public Dictionary<string, object> GetProcessInfo()
        {
            var info = new Dictionary<string, object>();

            if (_stateData != null)
            {
                info["CurrentState"] = _stateData.CurrentState.ToString() ?? "null";
                info["ProcessState"] = _stateData.ProcessState.ToString();
                info["IsFinished"] = _stateData.IsFinished;
                info["RetryCount"] = _stateData.RetryCount;
                info["FailureReason"] = _stateData.FailureReason.ToString();
                info["CorrelationId"] = _stateData.CorrelationId ?? "null";
                info["CreatedAt"] = _stateData.CreatedAt;
                info["UpdatedAt"] = _stateData.UpdatedAt;
            }
            else
            {
                info["Status"] = "Not initialized";
            }

            info["LastIterationFailed"] = _lastIterationFailed;
            info["LastException"] = _lastException?.Message ?? "null";
            info["ConfiguredTransitions"] = _transitions.Keys.Select(k => $"{k.FromState} -> {k.ToState}").ToArray();
            info["MaxRetries"] = Configuration.MaxRetries;
            info["TimeoutBehavior"] = Configuration.TimeoutBehavior.ToString();

            // Add current transition information
            if (_stateData != null)
            {
                bool isForward = _stateData.ProcessState == MultiStepProcessState.Running;
                var transitionInfo = GetTransitionForExecution(_stateData.CurrentState, isForward);

                if (transitionInfo != null)
                {
                    info["CurrentTransition"] = $"{transitionInfo.Value.transition.FromState} -> {transitionInfo.Value.transition.ToState}";
                    info["CurrentTransitionDirection"] = isForward ? "Forward" : "Rollback";

                    var details = isForward ? transitionInfo.Value.transition.ForwardDetails : transitionInfo.Value.transition.RollbackDetails;
                    info["CurrentTaskTimeout"] = details.Timeout?.ToString() ?? "null (uses process default)";
                    info["CurrentTaskMaxRetries"] = details.MaxRetries?.ToString() ?? "null (uses process default)";
                    info["CurrentTaskEffectiveMaxRetries"] = details.GetEffectiveMaxRetries(Configuration.MaxRetries);
                }
                else
                {
                    info["CurrentTransition"] = "Terminal state - no outgoing transitions";
                }
            }

            return info;
        }
    }
}
