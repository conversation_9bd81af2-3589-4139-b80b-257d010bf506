using shared.Components.MultiStepProcess.Enums;
using shared.Models.Interfaces;

namespace shared.Components.MultiStepProcess.Models
{
    /// <summary>
    /// Defines a state transition in a multi-step process.
    /// Contains the source state, target state, and the task details for forward and rollback operations.
    /// </summary>
    /// <typeparam name="TState">The type of state used by the IStateful object</typeparam>
    /// <typeparam name="TObject">The type of the payload object that implements IStateful</typeparam>
    /// <typeparam name="TResult">The type of result returned by the tasks</typeparam>
    public class MultiStepProcessStateTransition<TState, TObject, TResult>
        where TObject : IStateful<TState>
        where TState : struct
    {
        /// <summary>
        /// The source state from which this transition starts.
        /// </summary>
        public TState FromState { get; }

        /// <summary>
        /// The target state to which this transition leads.
        /// </summary>
        public TState ToState { get; }

        /// <summary>
        /// Details for the forward transition task (from FromState to ToState).
        /// </summary>
        public MultiStepProcessTransitionDetails<TObject, TResult> ForwardDetails { get; }

        /// <summary>
        /// Details for the rollback transition task (from ToState to FromState).
        /// </summary>
        public MultiStepProcessTransitionDetails<TObject, TResult> RollbackDetails { get; }

        /// <summary>
        /// Optional description of what this transition does.
        /// </summary>
        public string? Description { get; }

        /// <summary>
        /// Creates a new state transition.
        /// </summary>
        /// <param name="fromState">The source state</param>
        /// <param name="toState">The target state</param>
        /// <param name="forwardDetails">Details for the forward transition task</param>
        /// <param name="rollbackDetails">Details for the rollback transition task</param>
        /// <param name="description">Optional description</param>
        public MultiStepProcessStateTransition(
            TState fromState,
            TState toState,
            MultiStepProcessTransitionDetails<TObject, TResult> forwardDetails,
            MultiStepProcessTransitionDetails<TObject, TResult> rollbackDetails,
            string? description = null)
        {
            FromState = fromState;
            ToState = toState;
            ForwardDetails = forwardDetails ?? throw new ArgumentNullException(nameof(forwardDetails));
            RollbackDetails = rollbackDetails ?? throw new ArgumentNullException(nameof(rollbackDetails));
            Description = description;
        }

        /// <summary>
        /// Executes the forward task with the given stateful object.
        /// </summary>
        /// <param name="statefulObject">The stateful object to pass to the task</param>
        /// <returns>The result of the forward task execution</returns>
        public async Task<(MultiStepProcessTaskRunResult, TResult?)> ExecuteForwardTaskAsync(TObject statefulObject)
        {
            return await ForwardDetails.ExecuteAsync(statefulObject);
        }

        /// <summary>
        /// Executes the rollback task with the given stateful object.
        /// </summary>
        /// <param name="statefulObject">The stateful object to pass to the task</param>
        /// <returns>The result of the rollback task execution</returns>
        public async Task<(MultiStepProcessTaskRunResult, TResult?)> ExecuteRollbackTaskAsync(TObject statefulObject)
        {
            return await RollbackDetails.ExecuteAsync(statefulObject);
        }

        /// <summary>
        /// Executes the forward task with the given stateful object and returns the result as object.
        /// </summary>
        /// <param name="statefulObject">The stateful object to pass to the task</param>
        /// <returns>The result of the forward task execution with result cast to object</returns>
        public async Task<(MultiStepProcessTaskRunResult, TResult?)> ExecuteForwardTaskAsObjectAsync(TObject statefulObject)
        {
            var (result, value) = await ForwardDetails.ExecuteAsync(statefulObject);
            return (result, value);
        }

        /// <summary>
        /// Executes the rollback task with the given stateful object and returns the result as object.
        /// </summary>
        /// <param name="statefulObject">The stateful object to pass to the task</param>
        /// <returns>The result of the rollback task execution with result cast to object</returns>
        public async Task<(MultiStepProcessTaskRunResult, TResult?)> ExecuteRollbackTaskAsObjectAsync(TObject statefulObject)
        {
            var (result, value) = await RollbackDetails.ExecuteAsync(statefulObject);
            return (result,value);
        }

        /// <summary>
        /// Gets the effective timeout for the specified direction.
        /// </summary>
        /// <param name="isForward">True for forward task, false for rollback task</param>
        /// <param name="defaultTimeout">Default timeout to use if no specific timeout is set</param>
        /// <returns>The effective timeout, or null if no timeout is configured</returns>
        public TimeSpan? GetTimeoutForDirection(bool isForward, TimeSpan? defaultTimeout = null)
        {
            return isForward
                ? ForwardDetails.GetEffectiveTimeout(defaultTimeout)
                : RollbackDetails.GetEffectiveTimeout(defaultTimeout);
        }

        /// <summary>
        /// Gets the effective max retries for the specified direction.
        /// </summary>
        /// <param name="isForward">True for forward task, false for rollback task</param>
        /// <param name="defaultMaxRetries">Default max retries to use if no specific max retries is set</param>
        /// <returns>The effective max retries</returns>
        public int GetEffectiveMaxRetries(bool isForward, int defaultMaxRetries)
        {
            return isForward
                ? ForwardDetails.GetEffectiveMaxRetries(defaultMaxRetries)
                : RollbackDetails.GetEffectiveMaxRetries(defaultMaxRetries);
        }

    }
}
