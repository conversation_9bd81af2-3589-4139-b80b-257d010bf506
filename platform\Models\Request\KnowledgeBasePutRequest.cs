using System.ComponentModel.DataAnnotations;

namespace platform.Models.Request
{
    public class KnowledgeBasePutRequest
    {
        [Required(ErrorMessage = "Name is required")]
        [MinLength(6, ErrorMessage = "Name must be at least 6 characters long")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "Description is required")]
        [MinLength(6, ErrorMessage = "Description must be at least 6 characters long")]
        public string Description { get; set; } = string.Empty;
    }
}
