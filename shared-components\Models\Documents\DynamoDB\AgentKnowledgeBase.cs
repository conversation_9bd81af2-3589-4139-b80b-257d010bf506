using Amazon.DynamoDBv2.DataModel;
using shared.Models.Enums;
using shared.Models.Interfaces;

namespace shared.Models.Documents.DynamoDB
{
    [DynamoDBTable(nameof(AgentKnowledgeBase))]
    public class AgentKnowledgeBase : DynamoDBModel, INoSQLStatic, IStateful<AgentKnowledgeBaseStatus>
    {
        public const string AgentIdKnowledgebaseIdHashIndex = "AgentId-KnowledgebaseId-index";
        public const string KnowledgebaseIdAgentIdHashIndex = "KnowledgebaseId-AgentId-index";

        [DynamoDBHashKey]
        public string AccountId { get; set; } = string.Empty;

        [DynamoDBRangeKey]
        [DynamoDBGlobalSecondaryIndexRangeKey(KnowledgebaseIdAgentIdHashIndex)]
        [DynamoDBGlobalSecondaryIndexHashKey(AgentIdKnowledgebaseIdHashIndex)]
        public string AgentId { get; set; } = string.Empty;
        
        [DynamoDBGlobalSecondaryIndexRangeKey(AgentIdKnowledgebaseIdHashIndex)]
        [DynamoDBGlobalSecondaryIndexHashKey(KnowledgebaseIdAgentIdHashIndex)]
        public string KnowledgebaseId { get; set; } = string.Empty;

        public AgentKnowledgeBaseStatus Status { get; set; } = AgentKnowledgeBaseStatus.ASSIGNING;

        public string Description { get; set; } = string.Empty;


        /// <summary>
        /// Gets the hash key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public new static string GetHashKeyPropertyName(string? indexName = null)
        {
            if (indexName == AgentIdKnowledgebaseIdHashIndex)
            {
                // KnowledgebaseId-AgentId-index hash key
                return nameof(AgentKnowledgeBase.AgentId);
            }else if(indexName == KnowledgebaseIdAgentIdHashIndex)
            {
                return nameof(AgentKnowledgeBase.KnowledgebaseId);
            }
            return nameof(AgentKnowledgeBase.AccountId);
        }

        /// <summary>
        /// Gets the range key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            if (indexName == AgentIdKnowledgebaseIdHashIndex)
            {
                // KnowledgebaseId-AgentId-index hash key
                return nameof(AgentKnowledgeBase.KnowledgebaseId);
            }
            else if (indexName == KnowledgebaseIdAgentIdHashIndex)
            {
                return nameof(AgentKnowledgeBase.AgentId);
            }
            return nameof(AgentKnowledgeBase.AgentId);
        }

        /// <summary>
        /// Gets the table name from DynamoDBTable attribute.
        /// </summary>
        /// <returns>Table name</returns>
        public static new string GetTableName()
        {
            return nameof(AgentKnowledgeBase);
        }
    }
}
