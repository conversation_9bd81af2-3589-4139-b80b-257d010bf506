﻿using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace shared.Converters
{
    /// <summary>
    /// Non-generic version of JsonTypedConverter that can handle any object type.
    /// Use this when you need to serialize objects of unknown types at compile time.
    /// </summary>
    public class JsonTypedConverter : JsonConverter<object>
    {
        readonly MethodInfo? method = typeof(JsonSerializer).GetMethods().Where(
            m => m.Name == nameof(JsonSerializer.Deserialize) &&
            m.IsGenericMethod == true &&
            m.GetParameters()[0].ParameterType == typeof(string) &&
            m.GetParameters()[1].ParameterType == typeof(System.Text.Json.JsonSerializerOptions)
            ).First();

        private void readFromReaderData(ref Utf8JsonReader reader, ref string? data)
        {
            MemoryStream buffer = new MemoryStream();
            int ident = 0;
            JsonTokenType lastToken = JsonTokenType.None;

            while (reader.Read())
            {
                switch (reader.TokenType)
                {
                    case JsonTokenType.StartObject:
                        ident++;
                        buffer.Write(Encoding.UTF8.GetBytes("{"));
                        break;
                    case JsonTokenType.EndObject:
                        ident--;
                        buffer.Write(Encoding.UTF8.GetBytes("}"));
                        if (ident == 0)
                        {
                            data = Encoding.UTF8.GetString(buffer.ToArray());
                            return;
                        }
                        break;
                    case JsonTokenType.StartArray:
                        ident++;
                        buffer.Write(Encoding.UTF8.GetBytes("["));
                        break;
                    case JsonTokenType.EndArray:
                        ident--;
                        buffer.Write(Encoding.UTF8.GetBytes("]"));
                        if (ident == 0)
                        {
                            data = Encoding.UTF8.GetString(buffer.ToArray());
                            return;
                        }
                        break;
                    case JsonTokenType.PropertyName:
                        if (lastToken != JsonTokenType.StartObject && lastToken != JsonTokenType.StartArray)
                        {
                            buffer.Write(Encoding.UTF8.GetBytes(","));
                        }
                        buffer.Write(Encoding.UTF8.GetBytes("\"" + reader.GetString() + "\":"));
                        break;
                    case JsonTokenType.String:
                        if (lastToken != JsonTokenType.PropertyName)
                        {
                            buffer.Write(Encoding.UTF8.GetBytes(","));
                        }
                        buffer.Write(Encoding.UTF8.GetBytes("\"" + reader.GetString() + "\""));
                        break;
                    case JsonTokenType.Number:
                        if (lastToken != JsonTokenType.PropertyName)
                        {
                            buffer.Write(Encoding.UTF8.GetBytes(","));
                        }
                        // Handle different numeric types
                        if (reader.TryGetInt64(out long longValue))
                        {
                            buffer.Write(Encoding.UTF8.GetBytes(longValue.ToString()));
                        }
                        else if (reader.TryGetDouble(out double doubleValue))
                        {
                            buffer.Write(Encoding.UTF8.GetBytes(doubleValue.ToString()));
                        }
                        else if (reader.TryGetDecimal(out decimal decimalValue))
                        {
                            buffer.Write(Encoding.UTF8.GetBytes(decimalValue.ToString()));
                        }
                        break;
                    case JsonTokenType.True:
                    case JsonTokenType.False:
                        if (lastToken != JsonTokenType.PropertyName)
                        {
                            buffer.Write(Encoding.UTF8.GetBytes(","));
                        }
                        buffer.Write(Encoding.UTF8.GetBytes(reader.GetBoolean().ToString().ToLower()));
                        break;
                    case JsonTokenType.Null:
                        if (lastToken != JsonTokenType.PropertyName)
                        {
                            buffer.Write(Encoding.UTF8.GetBytes(","));
                        }
                        buffer.Write(Encoding.UTF8.GetBytes("null"));
                        break;
                }
                lastToken = reader.TokenType;
            }
        }

        private void readFromReaderObject(ref Utf8JsonReader reader, ref string sourceType, ref string? data)
        {
            Assembly? assembly = null;

            while (reader.Read())
            {
                switch (reader.TokenType)
                {
                    case JsonTokenType.EndObject:
                        return;
                    case JsonTokenType.PropertyName:
                        if (reader.GetString() == "$type")
                        {
                            reader.Read();
                            sourceType = reader.GetString() ?? typeof(object).Name;
                        }
                        else if (reader.GetString() == "$data")
                        {
                            readFromReaderData(ref reader, ref data);
                        }
                        break;
                }
            }
        }

        public override object? Read(
            ref Utf8JsonReader reader,
            Type typeToConvert,
            JsonSerializerOptions options)
        {
            string sourceType = string.Empty;
            string? data = null;
            object? result = null;

            readFromReaderObject(ref reader, ref sourceType, ref data);

            var assembly = Assembly.Load(sourceType.Split(".")[0]);
            Type? t = assembly.GetType(sourceType);
            if (t != null && method != null)
            {
                MethodInfo generic = method.MakeGenericMethod(t);
                dynamic? loadedJson = generic.Invoke(this, new object[] { data ?? string.Empty, options });
                if (loadedJson != null)
                {
                    result = loadedJson;
                }
            }

            return result;
        }

        public override void Write(
            Utf8JsonWriter writer,
            object obj,
            JsonSerializerOptions options)
        {
            writer.WriteStartObject();
            string sourceType = obj.GetType().FullName ?? obj.GetType().Name;
            writer.WriteString("$type", sourceType);
            writer.WritePropertyName("$data");

            // Always serialize with the actual runtime type
            JsonSerializer.Serialize(writer, obj, obj.GetType(), options);

            writer.WriteEndObject();
        }
    }

    /// <summary>
    /// Generic version of JsonTypedConverter for backward compatibility.
    /// </summary>
    /// <typeparam name="T">The type to convert</typeparam>
    public class JsonTypedConverter<T> : JsonConverter<T> where T : notnull
    {
        readonly MethodInfo? method = typeof(JsonSerializer).GetMethods().Where(
            m => m.Name == nameof(JsonSerializer.Deserialize) &&
            m.IsGenericMethod == true &&
            m.GetParameters()[0].ParameterType == typeof(string) &&
            m.GetParameters()[1].ParameterType == typeof(System.Text.Json.JsonSerializerOptions)
            ).First();


        private void readFromReaderData(ref Utf8JsonReader reader, ref string? data)
        {
            MemoryStream buffer = new MemoryStream();
            int ident = 0;
            JsonTokenType lastToken = JsonTokenType.None;
            
            while (reader.Read())
            {
                switch (reader.TokenType)
                {
                    case JsonTokenType.StartObject:
                        ident++;
                        buffer.Write(reader.ValueSpan);
                        break;
                    case JsonTokenType.EndObject:
                        ident--;
                        buffer.Write(reader.ValueSpan);
                        if (ident == 0)
                        {
                            data = Encoding.UTF8.GetString(buffer.ToArray());
                            return;
                        }
                        break;
                    case JsonTokenType.PropertyName:
                        if(lastToken != JsonTokenType.StartObject) buffer.Write(Encoding.UTF8.GetBytes(","));
                        buffer.Write(Encoding.UTF8.GetBytes("\""));
                        buffer.Write(reader.ValueSpan);
                        buffer.Write(Encoding.UTF8.GetBytes("\":"));
                        break;
                    case JsonTokenType.StartArray: buffer.Write(reader.ValueSpan); break;
                    case JsonTokenType.EndArray: buffer.Write(reader.ValueSpan); break;
                    case JsonTokenType.String:
                        buffer.Write(Encoding.UTF8.GetBytes("\""));
                        buffer.Write(reader.ValueSpan);
                        buffer.Write(Encoding.UTF8.GetBytes("\""));
                        break;
                    case JsonTokenType.Comment:
                        break;
                    default:
                        buffer.Write(reader.ValueSpan);
                        break;
                }
                lastToken = reader.TokenType;
            }
        }

        private void readFromReaderObject(ref Utf8JsonReader reader, ref string sourceType, ref string? data)
        {
            Assembly? assembly = null;

            while (reader.Read())
            {
                switch (reader.TokenType)
                {
                    case JsonTokenType.EndObject:
                        return;
                    case JsonTokenType.PropertyName:
                        if (reader.GetString() == "$type")
                        {
                            reader.Read();
                            sourceType = reader.GetString() ?? typeof(object).Name;
                        }
                        else if (reader.GetString() == "$data")
                        {
                            readFromReaderData(ref reader, ref data);
                        }
                        break;
                }
            }
        }

        public override T? Read(
            ref Utf8JsonReader reader,
            Type typeToConvert,
            JsonSerializerOptions options)
        {
            //string sourceType = typeof(object).Name;
            string sourceType = string.Empty;
            string? data = null;
            T? result = default(T);

            readFromReaderObject(ref reader, ref sourceType, ref data);

            var assembly = Assembly.Load(sourceType.Split(".")[0]);
            Type? t = assembly.GetType(sourceType);
            if (t != null && method != null)
            {
                MethodInfo generic = method.MakeGenericMethod(t);
                dynamic? loadedJson = generic.Invoke(this, new object[] { data ?? string.Empty, options });
                if (loadedJson != null)
                {
                    result = loadedJson;
                }
            }

            return result;
        }

        public override void Write(
            Utf8JsonWriter writer,
            T obj,
            JsonSerializerOptions options)
        {
            writer.WriteStartObject();
            string sourceType = obj.GetType().FullName ?? obj.GetType().Name;
            //string sourceType = obj.GetType().Name;
            writer.WriteString("$type", sourceType);
            writer.WritePropertyName("$data");

            // Check if T is the same as the actual object type
            Type actualType = obj.GetType();
            Type genericType = typeof(T);

            if (genericType == actualType || genericType == typeof(object))
            {
                // If T is object or matches the actual type, serialize with the actual type
                JsonSerializer.Serialize(writer, obj, actualType, options);
            }
            else
            {
                // Fallback to original behavior for exact type matches
                JsonSerializer.Serialize(writer, obj, options);
            }

            writer.WriteEndObject();
        }
    }

    /// <summary>
    /// Custom JsonConverter attribute that dynamically creates the appropriate JsonTypedConverter<T>
    /// for the property type at runtime. This solves the compile-time type compatibility issue.
    /// </summary>
    public class JsonTypedConverterAttribute : JsonConverterAttribute
    {
        public JsonTypedConverterAttribute()
        {
        }

        public override JsonConverter? CreateConverter(Type typeToConvert)
        {
            // Create the generic type JsonTypedConverter<typeToConvert>
            Type converterType = typeof(JsonTypedConverter<>).MakeGenericType(typeToConvert);

            // Create an instance of the converter
            return (JsonConverter?)Activator.CreateInstance(converterType);
        }
    }
}
