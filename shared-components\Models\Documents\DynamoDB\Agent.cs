using Amazon.DynamoDBv2.DataModel;
using shared.Converters;
using shared.Models.Enums;
using shared.Models.Interfaces;
using System.Text.Json.Serialization;

namespace shared.Models.Documents.DynamoDB
{
    [DynamoDBTable(nameof(Agent))]
    public class Agent : DynamoDBModel, INoSQLStatic, IStateful<AgentStatus>
    {

        [DynamoDBHashKey]
        public string AccountId { get; set; } = string.Empty;

        [DynamoDBRangeKey]
        public string AgentId { get; set; } = string.Empty;

        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string DefaultPrompt { get; set; } = string.Empty;

        [DynamoDBProperty(typeof(DynamoEnumStringConverter<AgentType>))]
        [JsonConverter(typeof(JsonEnumStringConverter<AgentType>))]
        public AgentType LLMModelType { get; set; } = AgentType.Claude_v2;

        [DynamoDBProperty(typeof(DynamoEnumStringConverter<AgentStatus>))]
        [JsonConverter(typeof(JsonEnumStringConverter<AgentStatus>))]
        public AgentStatus Status { get; set; }  = AgentStatus.QUEUED;

        public override string GetSearchString()
        {
            return Name.ToLower();
        }

        /// <summary>
        /// Gets the hash key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Hash key property name</returns>
        public static new string GetHashKeyPropertyName(string? indexName = null)
        {
            return nameof(Agent.AccountId);
        }

        /// <summary>
        /// Gets the range key property name for DynamoDB table or index.
        /// </summary>
        /// <param name="indexName">Optional index name for secondary indexes</param>
        /// <returns>Range key property name</returns>
        public static new string? GetRangeKeyPropertyName(string? indexName = null)
        {
            return nameof(Agent.AgentId);
        }

        /// <summary>
        /// Gets the table name from DynamoDBTable attribute.
        /// </summary>
        /// <returns>Table name</returns>
        public static new string GetTableName()
        {
            return nameof(Agent);
        }
    }
}
